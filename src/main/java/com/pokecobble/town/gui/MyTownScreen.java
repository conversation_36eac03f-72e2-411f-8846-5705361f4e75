package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.access.TownAccessController;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import com.pokecobble.town.network.election.ElectionNetworkHandler;
import com.pokecobble.town.gui.VoteConfirmationScreen;
import com.pokecobble.town.claim.ClaimHistoryEntry;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.gui.ClaimHistoryScreen;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import com.pokecobble.town.network.town.TownNetworkHandler;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import net.minecraft.util.Formatting;
import com.pokecobble.town.util.TownImageUtil;
import com.mojang.blaze3d.systems.RenderSystem;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Comparator;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * Screen for managing the player's town with subcategories.
 */
public class MyTownScreen extends Screen implements TownDataSynchronizer.TownPlayerUpdateCallback, ElectionNetworkHandler.ElectionUpdateCallback, com.pokecobble.ui.UIDataRefreshManager.RefreshableComponent {
    private final Screen parent;

    // Panel dimensions
    private int panelWidth = 400;
    private int panelHeight = 300;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000; // Background color for masking

    // Subcategories
    private List<TownSubcategory> subcategories = new ArrayList<>();
    private TownSubcategory selectedSubcategory;

    // Scrolling
    private int playersScrollOffset = 0;
    private int electionScrollOffset = 0;

    // Cache for player list to prevent regeneration on every frame
    private static final Random RANDOM = new Random();
    private UUID lastTownId = null;
    private List<TownPlayer> cachedPlayerList = null;

    // Player list sorting and filtering
    private enum SortType { RANK, NAME, NEWEST, OLDEST }
    private enum FilterType { ALL, ONLINE, OFFLINE }
    private SortType currentSortType = SortType.RANK;
    private FilterType currentFilterType = FilterType.ALL;
    private String searchQuery = "";
    private TextFieldWidget searchField;

    // Invite button state
    private int[] inviteButtonBounds = null;
    private boolean showInviteScreen = false;

    // Status message
    private Text statusText = Text.empty();
    private int statusColor = 0xFFFFFF;

    // Circle position and size for the change town button
    private int circleX;
    private int circleY;
    private int circleSize;
    private int circleCenterX;
    private int circleCenterY;
    private int circleRadius;



    public MyTownScreen(Screen parent) {
        super(Text.literal("My Town"));
        this.parent = parent;
    }

    /**
     * Checks if the player has permission to access a subcategory.
     *
     * @param subcategoryName The name of the subcategory
     * @param playerTown The player's town
     * @return True if the player has permission, false otherwise
     */
    private boolean hasPermissionForSubcategory(String subcategoryName, Town playerTown) {
        if (client.player == null || playerTown == null) {
            return false;
        }

        return TownAccessController.getInstance().hasPermissionForSubcategory(
            subcategoryName, playerTown, client.player.getUuid());
    }

    /**
     * Renders a permission denied message for a subcategory.
     *
     * @param context The draw context
     * @param subcategoryName The name of the subcategory
     * @param contentX The X position of the content area
     * @param contentY The Y position of the content area
     * @param contentWidth The width of the content area
     * @param contentHeight The height of the content area
     */
    private void renderPermissionDeniedMessage(DrawContext context, String subcategoryName, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Draw a semi-transparent background
        context.fillGradient(contentX, contentY, contentX + contentWidth, contentY + contentHeight, 0x80202020, 0x80303030);

        // Calculate center position
        int centerX = contentX + contentWidth / 2;
        int centerY = contentY + contentHeight / 2;

        // Draw lock icon
        String lockIcon = "🔒";
        int lockWidth = this.textRenderer.getWidth(lockIcon);
        context.drawTextWithShadow(this.textRenderer, lockIcon, centerX - lockWidth / 2, centerY - 40, 0xFFFF5555);

        // Draw permission denied message
        Text deniedText = Text.literal("Access Denied").formatted(Formatting.BOLD, Formatting.RED);
        context.drawCenteredTextWithShadow(this.textRenderer, deniedText, centerX, centerY - 20, 0xFFFFFF);

        // Draw explanation
        String permissionCategory = TownAccessController.getInstance().getPermissionCategory(subcategoryName);
        String permissionName = TownAccessController.getInstance().getPermissionName(subcategoryName);

        if (permissionCategory != null && permissionName != null) {
            Text explanationText = Text.literal("You need the permission: " + permissionName)
                    .formatted(Formatting.YELLOW);
            context.drawCenteredTextWithShadow(this.textRenderer, explanationText, centerX, centerY, 0xFFFFFF);
        }

        // Draw instruction
        Text instructionText = Text.literal("Ask the mayor or an admin for access")
                .formatted(Formatting.WHITE);
        context.drawCenteredTextWithShadow(this.textRenderer, instructionText, centerX, centerY + 20, 0xFFFFFF);
    }

    @Override
    protected void init() {
        super.init();

        // Check if player is still in a town - if not, close this screen
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();

            if (playerTown == null && playerTownId == null) {
                // Player is definitely not in a town, close this screen and return to parent
                com.pokecobble.Pokecobbleclaim.LOGGER.info("MyTownScreen: Player not in any town, closing screen");
                this.close();
                return;
            } else if (playerTown == null && playerTownId != null) {
                // Player has a town ID but town data is not cached - request fresh data
                com.pokecobble.Pokecobbleclaim.LOGGER.info("MyTownScreen: Player has town ID " + playerTownId + " but town data not cached, requesting fresh data");
                com.pokecobble.town.network.town.TownNetworkHandler.requestTownData();
                // Continue with initialization - the screen will handle loading states
            }
        }

        // Calculate panel dimensions based on screen size - make it even wider
        panelWidth = Math.min(width - 20, 1100); // Increased from 1000 to 1100 and reduced side margins
        panelHeight = height - 20;

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = 10;

        // Register for town player updates
        TownDataSynchronizer.registerPlayerUpdateCallback(this);

        // Register for election updates
        ElectionNetworkHandler.registerElectionUpdateCallback(this);

        // Request the latest town data from the server
        TownNetworkHandler.requestTownData();

        // Request the latest claim history from the server if player is in a town
        if (client.player != null) {
            Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
            if (playerTown != null) {
                com.pokecobble.town.network.town.ClaimHistorySynchronizer.requestClaimHistory(playerTown.getId());

                // Request the latest town image data
                TownImageSynchronizer.requestTownImageUpdate(playerTown.getId());
            }
        }

        // We'll use a custom back button instead of the default ButtonWidget
        // The button will be drawn in the render method and handled in mouseClicked

        // Create search field for the Players subcategory
        searchField = new TextFieldWidget(this.textRenderer, 0, 0, 120, 16, Text.literal("Search"));
        searchField.setMaxLength(50);
        searchField.setVisible(false); // Only visible when Players subcategory is selected
        searchField.setChangedListener(this::onSearchFieldChanged);
        addDrawableChild(searchField);

        // Setup subcategories
        setupSubcategories();
    }

    /**
     * Sets up the subcategories for the town management interface.
     */
    private void setupSubcategories() {
        subcategories.clear();

        // Get player town from client-side manager
        Town playerTown = null;
        boolean isDataLoading = false;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

            // If playerTown is null but we have a playerTownId, it means the cache expired
            // Request fresh data from the server but continue to show subcategories
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();
            if (playerTown == null && playerTownId != null) {
                TownNetworkHandler.requestTownData();
                isDataLoading = true; // Mark that we're waiting for data
                // Don't return early - continue to show subcategories
            }
        }

        // Check if there's an active election using client-side manager
        boolean hasElection = false;
        if (playerTown != null) {
            Election election = com.pokecobble.town.client.ClientElectionManager.getInstance().getElection(playerTown.getId());
            hasElection = (election != null);

            // Force refresh of player list when election status changes
            if (hasElection != subcategories.stream().anyMatch(sc -> sc.getName().equals("Election"))) {
                refreshPlayerList();
            }
        }

        // Add subcategories - always show them even when data is loading
        subcategories.add(new TownSubcategory("Main", "🏠", 0xFF2196F3)); // House icon

        // Replace Players with Election during an election (only if we have valid data)
        if (!isDataLoading && hasElection) {
            subcategories.add(new TownSubcategory("Election", "🗳", 0xFFE91E63)); // Ballot box icon
        } else {
            subcategories.add(new TownSubcategory("Players", "👥", 0xFF4CAF50)); // People icon
        }

        subcategories.add(new TownSubcategory("Claims", "🔒", 0xFFE91E63)); // Lock icon
        subcategories.add(new TownSubcategory("Level", "⭐", 0xFFFFAA00)); // Star icon
        subcategories.add(new TownSubcategory("Bank", "💰", 0xFFFFD700)); // Money bag icon
        subcategories.add(new TownSubcategory("Settings", "⚙", 0xFF9E9E9E)); // Gear icon

        // Select the first subcategory by default
        if (!subcategories.isEmpty()) {
            selectedSubcategory = subcategories.get(0);
        } else {
            selectedSubcategory = null;
        }

        // Initialize subcategory positions
        initializeSubcategoryPositions();
    }

    /**
     * Initializes the positions of subcategory buttons.
     * This ensures they're positioned correctly for click detection before the first render.
     */
    private void initializeSubcategoryPositions() {
        // Calculate positions - same as in render method
        int leftX = (width - panelWidth) / 2;
        int topY = 10;
        int contentY = topY + 25;
        int sidebarX = leftX + 5;
        int sidebarY = contentY;

        // Position subcategory buttons
        int subcategoryY = sidebarY + 5;
        int subcategoryHeight = 26;
        int subcategorySpacing = 3;

        for (TownSubcategory subcategory : subcategories) {
            // Store position for click detection
            subcategory.setPosition(sidebarX + 5, subcategoryY);

            // Move to next subcategory
            subcategoryY += subcategoryHeight + subcategorySpacing;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = 10;

        // Draw panel background
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, 0xD0101010, 0xD0202030);

        // Draw even smaller header
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 20, 0xA0303050, 0xA0404060);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, "My Town", width / 2, topY + 6, 0xFFFFFF);

        // Get player town from client-side manager
        Town playerTown = null;
        boolean isDataLoading = false;
        if (client.player != null) {
            playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();

            // If playerTown is null but we have a playerTownId, it means the cache expired
            // Request fresh data from the server but continue to show the UI
            UUID playerTownId = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTownId();
            if (playerTown == null && playerTownId != null) {
                // Request fresh town data from server
                TownNetworkHandler.requestTownData();
                isDataLoading = true; // Mark that we're waiting for data
                // Don't return early - continue to show the UI with loading indicators
            }
        }

        if (playerTown == null) {
            // If player is no longer in a town and we're not loading data, close the screen
            if (!isDataLoading) {
                this.close();
                return;
            }

            // Show loading message while waiting for data
            context.drawCenteredTextWithShadow(this.textRenderer,
                isDataLoading ? "Loading town data..." : "You are not a member of any town",
                width / 2, height / 2, 0xFFFFFF);
        } else {
            // Calculate content area dimensions - more vertical space
            int contentX = leftX + 10;
            int contentY = topY + 25; // Start even closer to header (reduced from 35 to 25)
            int contentWidth = panelWidth - 20;
            int contentHeight = panelHeight - 35; // Extend even closer to bottom (reduced from 45 to 35)

            // Calculate sidebar dimensions - narrower and moved to the left
            int sidebarWidth = 90; // Reduced from 130 to 90
            int sidebarX = leftX + 5; // Moved more to the left (from contentX to leftX + 5)
            int sidebarY = contentY;
            int sidebarHeight = contentHeight;

            // Draw sidebar background
            context.fill(sidebarX, sidebarY, sidebarX + sidebarWidth, sidebarY + sidebarHeight, 0x40000000);

            // Draw subcategory buttons - larger and more spaced
            int subcategoryY = sidebarY + 5;
            int subcategoryHeight = 26; // Increased from 22 to 26
            int subcategorySpacing = 3; // Increased from 2 to 3

            for (TownSubcategory subcategory : subcategories) {
                // Store position for click detection
                subcategory.setPosition(sidebarX + 5, subcategoryY);

                // Check if this subcategory is selected
                boolean isSelected = subcategory == selectedSubcategory;

                // Check if mouse is hovering over this subcategory
                boolean isHovered = mouseX >= sidebarX + 5 && mouseX <= sidebarX + sidebarWidth - 5 &&
                                   mouseY >= subcategoryY && mouseY <= subcategoryY + subcategoryHeight;

                // Draw subcategory button background
                int bgColor = isSelected ? subcategory.getColor() : (isHovered ? 0x80505050 : 0x60303030);
                context.fill(sidebarX + 5, subcategoryY, sidebarX + sidebarWidth - 5, subcategoryY + subcategoryHeight, bgColor);

                // Draw subtle glass effect
                context.fill(sidebarX + 5, subcategoryY, sidebarX + sidebarWidth - 5, subcategoryY + 1, 0x20FFFFFF);
                context.fill(sidebarX + 5, subcategoryY, sidebarX + 6, subcategoryY + subcategoryHeight, 0x20FFFFFF);
                context.fill(sidebarX + 5, subcategoryY + subcategoryHeight - 1, sidebarX + sidebarWidth - 5, subcategoryY + subcategoryHeight, 0x20000000);
                context.fill(sidebarX + sidebarWidth - 6, subcategoryY, sidebarX + sidebarWidth - 5, subcategoryY + subcategoryHeight, 0x20000000);

                // Draw subcategory name with icon - compact for narrower sidebar
                String subcategoryText = subcategory.getIcon() + " " + subcategory.getName();
                context.drawTextWithShadow(this.textRenderer, subcategoryText,
                    sidebarX + 8, subcategoryY + (subcategoryHeight - 8) / 2, 0xFFFFFF);

                // Move to next subcategory
                subcategoryY += subcategoryHeight + subcategorySpacing;
            }



            // Draw content area - adjusted for moved sidebar
            int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
            int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width

            // Draw content area background
            context.fill(contentAreaX, sidebarY, contentAreaX + contentAreaWidth, sidebarY + sidebarHeight, 0x30000000);

            // Hide search field by default (only show in Players subcategory)
            searchField.setVisible(false);

            // Render the selected subcategory content
            if (selectedSubcategory != null) {
                String subcategoryName = selectedSubcategory.getName();

                // Render the subcategory content
                if (!subcategories.isEmpty() && selectedSubcategory == subcategories.get(0)) {
                    // Store the circle position as class variables for click detection in mouseClicked
                    // These should match the values in renderMainSubcategory
                    circleSize = 50; // Size of the circular shape (slightly smaller)
                    circleX = contentAreaX + 10; // Position more to the left
                    circleY = sidebarY + 20; // Position slightly down

                    // Calculate and store circle center and radius
                    circleCenterX = circleX + circleSize/2;
                    circleCenterY = circleY + circleSize/2;
                    circleRadius = circleSize/2;

                    renderMainSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight, isDataLoading);
                } else if (subcategoryName.equals("Players")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderPlayersSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Election")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderElectionSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Claims")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderClaimsSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Level")) {
                    // Temporarily show a placeholder message instead of the actual level subcategory
                    renderLevelPlaceholderMessage(context, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);

                    // Original code (commented out for easy restoration later)
                    /*
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderLevelSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                    */
                } else if (subcategoryName.equals("Bank")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderBankSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                } else if (subcategoryName.equals("Settings")) {
                    if (hasPermissionForSubcategory(subcategoryName, playerTown)) {
                        renderSettingsSubcategory(context, mouseX, mouseY, playerTown, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    } else {
                        renderPermissionDeniedMessage(context, subcategoryName, contentAreaX, sidebarY, contentAreaWidth, sidebarHeight);
                    }
                }
            }
        }

        // Draw status message if present
        if (statusText != null && !statusText.getString().isEmpty()) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusText, this.width / 2, 5, statusColor);
        }

        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Renders the Main subcategory content.
     */
    private void renderMainSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight, boolean isDataLoading) {
        // If data is loading, show loading message
        if (isDataLoading || playerTown == null) {
            String message = isDataLoading ? "Loading town data..." : "No town data available";
            int messageWidth = this.textRenderer.getWidth(message);
            context.drawTextWithShadow(this.textRenderer, message,
                contentX + (contentWidth - messageWidth) / 2, contentY + contentHeight / 2, 0xFFFFFF);
            return;
        }

        // Calculate positions for the circular shape and town info
        int circleSize = 50; // Size of the circular shape (slightly smaller)
        int circleX = contentX + 10; // Position the circle more to the left
        int circleY = contentY + 20; // Position slightly down

        // Calculate town info position (to the right of the circle)
        int infoX = circleX + circleSize + 15; // Position town info to the right of the circle
        int infoWidth = contentWidth - circleSize - 40; // Width of the info area, leaving space for the circle
        int rowHeight = 16; // Height of each row
        int sectionSpacing = 10; // Spacing between sections
        int currentY = contentY + 12; // Start position from the top (very slightly down)

        // Calculate circle center and radius
        float centerX = circleX + circleSize/2;
        float centerY = circleY + circleSize/2;
        float radius = circleSize/2;

        // Check if mouse is hovering over the circle
        boolean isCircleHovered = isPointInCircle(mouseX, mouseY, (int)centerX, (int)centerY, (int)radius);

        // Draw a filled circle with a nice color
        int circleFillColor = isCircleHovered ? 0x8042A5F5 : 0x80336699; // Semi-transparent blue color, lighter when hovered
        drawCircle(context, centerX, centerY, radius, circleFillColor, true);

        // Get town image if available
        String imageName = playerTown.getImage();
        Identifier townImageId = null;

        if (imageName != null && !imageName.isEmpty()) {
            // Don't log for default images to reduce spam
            if (!"default".equals(imageName)) {
                townImageId = TownImageUtil.getImageIdentifier(playerTown, imageName);
                // Log the image loading attempt only for non-default images
                com.pokecobble.Pokecobbleclaim.LOGGER.debug("Loading town image: " + imageName + " for town: " + playerTown.getName() + ", result: " + (townImageId != null ? "success" : "not found"));
            } else {
                // Just get the identifier without logging for default images
                townImageId = TownImageUtil.getImageIdentifier(playerTown, imageName);
            }
        }

        // Draw a white border around the circle
        drawCircle(context, centerX, centerY, radius, 0x80FFFFFF, false);

        // Draw hover text when hovered
        if (isCircleHovered) {
            String hoverText = "Change Town Image";
            int hoverTextWidth = this.textRenderer.getWidth(hoverText);
            // Position the hover text below the circle
            context.drawTextWithShadow(this.textRenderer, hoverText,
                (int)(centerX - hoverTextWidth/2), (int)(centerY + radius + 5), 0xFFFFFF);
        }

        // Draw town info on the right side
        // Draw town name as header
        String townName = playerTown.getName();
        context.drawTextWithShadow(this.textRenderer, Text.literal(townName).formatted(Formatting.BOLD),
            infoX - 15, currentY, 0xFFFFFF);
        currentY += rowHeight + 5; // Add a bit more space after the town name

        // Draw town info in a grid layout
        // Description
        context.drawTextWithShadow(this.textRenderer, Text.literal("Description:").formatted(Formatting.ITALIC),
            infoX - 5, currentY, 0xAAAAAA);
        String description = playerTown.getDescription();
        // Truncate description if too long
        if (description.length() > 25) {
            description = description.substring(0, 22) + "...";
        }
        context.drawTextWithShadow(this.textRenderer, description,
            infoX + 65, currentY, 0xFFFFFF);
        currentY += rowHeight;

        // Status
        context.drawTextWithShadow(this.textRenderer, Text.literal("Status:").formatted(Formatting.ITALIC),
            infoX - 5, currentY, 0xAAAAAA);
        String statusText;
        int statusColor;

        switch (playerTown.getJoinType()) {
            case OPEN:
                statusText = "Open";
                statusColor = 0x55FF55; // Green
                break;
            case CLOSED:
                statusText = "Closed";
                statusColor = 0xFF5555; // Red
                break;
            case INVITE_ONLY:
                statusText = "Invite Only";
                statusColor = 0x5555FF; // Blue
                break;
            default:
                statusText = "Unknown";
                statusColor = 0xAAAAAA; // Gray
                break;
        }
        context.drawTextWithShadow(this.textRenderer, statusText,
            infoX + 65, currentY, statusColor);
        currentY += rowHeight + 5; // Add a bit more space after the status

        // Player count
        context.drawTextWithShadow(this.textRenderer, Text.literal("Players:").formatted(Formatting.ITALIC),
            infoX - 15, currentY, 0xAAAAAA);
        String playerCountText = playerTown.getPlayerCount() + "/" + playerTown.getMaxPlayers();
        context.drawTextWithShadow(this.textRenderer, playerCountText,
            infoX + 55, currentY, 0xFFFFFF);
        currentY += rowHeight + sectionSpacing;

        // Town Ratings Section - use the full width of the content area
        renderTownRatings(context, mouseX, mouseY, playerTown, contentX, currentY, contentWidth, contentHeight - currentY + contentY);

        // Leave Town button has been moved to the Settings subcategory
    }

    /**
     * Renders the Players subcategory content.
     */
    private void renderPlayersSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Use the class field for scrolling

        // Layout variables
        int leftMargin = contentX + 15;
        int rowHeight = 18; // More compact row height
        int currentY = contentY + 10; // Reduced top margin

        // Draw column headers
        int rankWidth = 50; // Smaller width for rank
        int nameWidth = 130; // Slightly wider for names
        int statusWidth = 45; // Narrower
        int actionWidth = 45; // Narrower

        // Make the player list smaller at the top to make room for controls
        int topMargin = 30; // Increased to make room for controls
        currentY = contentY + topMargin; // Start lower to make room for controls

        // Position controls within the content area
        int controlsY = contentY + 8; // Position controls inside the content area

        // Position and show search field - directly above the Name column
        int searchFieldX = leftMargin + rankWidth + 5;
        int searchFieldY = controlsY;
        searchField.setX(searchFieldX);
        searchField.setY(searchFieldY);
        searchField.setWidth(nameWidth - 20); // Slightly narrower to fit better with filter button
        searchField.setVisible(true);

        // Draw sort button above the Rank column - smaller and more compact
        int sortButtonWidth = 40;
        int sortButtonHeight = 14;
        int sortButtonY = controlsY;
        int sortButtonX = leftMargin + 5; // Above Rank column

        // Draw sort cycle button (cycles through rank, name, newest, oldest)
        String sortText;
        int sortColor;
        switch (currentSortType) {
            case RANK:
                sortText = "Rank ↓";
                sortColor = 0xFF2196F3;
                break;
            case NAME:
                sortText = "Name ↓";
                sortColor = 0xFF4CAF50;
                break;
            case NEWEST:
                sortText = "New ↓";
                sortColor = 0xFFFF9800;
                break;
            case OLDEST:
                sortText = "Old ↓";
                sortColor = 0xFFE91E63;
                break;
            default:
                sortText = "Sort";
                sortColor = 0xFF555555;
                break;
        }

        drawModernButton(context, sortButtonX, sortButtonY, sortButtonWidth, sortButtonHeight,
            sortColor,
            mouseX >= sortButtonX && mouseX <= sortButtonX + sortButtonWidth &&
            mouseY >= sortButtonY && mouseY <= sortButtonY + sortButtonHeight, true);
        context.drawCenteredTextWithShadow(this.textRenderer, sortText,
            sortButtonX + sortButtonWidth / 2, sortButtonY + 3, 0xFFFFFF);

        // No sort label

        // Draw filter button above the Status column - smaller and more compact
        int filterButtonWidth = 40;
        int filterButtonHeight = 14;
        int filterButtonY = controlsY;
        int filterButtonX = leftMargin + rankWidth + nameWidth + 5; // Above Status column

        // Draw filter cycle button (cycles through all, online, offline)
        String filterText;
        int filterColor;
        switch (currentFilterType) {
            case ALL:
                filterText = "All";
                filterColor = 0xFF2196F3;
                break;
            case ONLINE:
                filterText = "Online";
                filterColor = 0xFF4CAF50;
                break;
            case OFFLINE:
                filterText = "Offline";
                filterColor = 0xFFFF5555;
                break;
            default:
                filterText = "Filter";
                filterColor = 0xFF555555;
                break;
        }

        drawModernButton(context, filterButtonX, filterButtonY, filterButtonWidth, filterButtonHeight,
            filterColor,
            mouseX >= filterButtonX && mouseX <= filterButtonX + filterButtonWidth &&
            mouseY >= filterButtonY && mouseY <= filterButtonY + filterButtonHeight, true);
        context.drawCenteredTextWithShadow(this.textRenderer, filterText,
            filterButtonX + filterButtonWidth / 2, filterButtonY + 3, 0xFFFFFF);

        // Draw invite button to the right of the filter button
        // Only show if player has permission to invite others
        boolean canInvite = false;
        if (client.player != null && playerTown != null) {
            UUID playerId = client.player.getUuid();
            TownPlayer townPlayer = playerTown.getPlayer(playerId);

            if (townPlayer != null) {
                // Check if player has the specific permission or is the mayor (who always has all permissions)
                canInvite = townPlayer.getRank() == TownPlayerRank.OWNER ||
                           townPlayer.hasPermission("Player Management", "Can invite players");
            } else {
                // Fallback to rank-based check if TownPlayer object is not available
                TownPlayerRank rank = playerTown.getPlayerRank(playerId);
                canInvite = rank == TownPlayerRank.OWNER || rank == TownPlayerRank.ADMIN;
            }
        }

        if (canInvite) {
            int inviteButtonWidth = 50;
            int inviteButtonHeight = 14;
            int inviteButtonY = controlsY;
            int inviteButtonX = filterButtonX + filterButtonWidth + 10; // To the right of filter button

            boolean inviteHovered = mouseX >= inviteButtonX && mouseX <= inviteButtonX + inviteButtonWidth &&
                                    mouseY >= inviteButtonY && mouseY <= inviteButtonY + inviteButtonHeight;

            // Make the invite button stand out with a different color
            int inviteColor = 0xFF9C27B0; // Purple to stand out

            drawModernButton(context, inviteButtonX, inviteButtonY, inviteButtonWidth, inviteButtonHeight,
                inviteColor, inviteHovered, true);
            context.drawCenteredTextWithShadow(this.textRenderer, "Invite",
                inviteButtonX + inviteButtonWidth / 2, inviteButtonY + 3, 0xFFFFFF);

            // Store the button bounds for click handling
            inviteButtonBounds = new int[]{inviteButtonX, inviteButtonY, inviteButtonWidth, inviteButtonHeight};
        } else {
            inviteButtonBounds = null;
        }

        // No filter label

        // Use the same column dimensions defined above

        // Draw header background
        context.fill(leftMargin, currentY, leftMargin + contentWidth - 30, currentY + rowHeight, 0x40000000);

        // Draw column headers
        context.drawTextWithShadow(this.textRenderer, Text.literal("Rank").formatted(Formatting.BOLD),
            leftMargin + 5, currentY + 5, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Name").formatted(Formatting.BOLD),
            leftMargin + rankWidth + 5, currentY + 5, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Status").formatted(Formatting.BOLD),
            leftMargin + rankWidth + nameWidth + 5, currentY + 5, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Actions").formatted(Formatting.BOLD),
            leftMargin + rankWidth + nameWidth + statusWidth + 5, currentY + 5, 0xFFFFFF);

        // Move currentY down to account for the header row
        currentY += rowHeight;

        // Calculate list area dimensions
        int listAreaY = currentY;
        int listAreaHeight = contentHeight - 20 - rowHeight - rowHeight; // Adjust height to account for header and prevent overflow

        // Draw list area background
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 30, listAreaY + listAreaHeight, 0x20000000);

        // Create dummy player list for testing
        List<TownPlayer> players = createDummyPlayerList(playerTown);

        // Calculate total height of all players
        int totalHeight = players.size() * rowHeight;

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - listAreaHeight);
        playersScrollOffset = Math.min(playersScrollOffset, maxScroll);

        // Draw scrollbar if needed
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(leftMargin + contentWidth - 25, listAreaY, leftMargin + contentWidth - 22, listAreaY + listAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(20, listAreaHeight * listAreaHeight / totalHeight);
            int scrollbarY = listAreaY + (listAreaHeight - scrollbarHeight) * playersScrollOffset / maxScroll;

            // Draw scrollbar handle
            context.fill(leftMargin + contentWidth - 25, scrollbarY, leftMargin + contentWidth - 22, scrollbarY + scrollbarHeight, 0x80FFFFFF);
        }

        // Draw player list with scrolling
        int playerY = listAreaY - playersScrollOffset;
        int visiblePlayers = 0;

        for (TownPlayer player : players) {
            // Skip if player is completely outside visible area
            if (playerY + rowHeight < listAreaY || playerY > listAreaY + listAreaHeight) {
                playerY += rowHeight;
                continue;
            }

            // Draw player entry background (alternating colors)
            int bgColor = visiblePlayers % 2 == 0 ? 0x20FFFFFF : 0x30FFFFFF;
            context.fill(leftMargin, playerY, leftMargin + contentWidth - 30, playerY + rowHeight, bgColor);

            // Draw rank icon and name with color (smaller font for name)
            TownPlayerRank rank = player.getRank();
            // Draw just the icon first
            context.drawTextWithShadow(this.textRenderer, rank.getIcon(),
                leftMargin + 5, playerY + 5, rank.getColor());

            // Calculate icon width to position name right after it
            int iconWidth = this.textRenderer.getWidth(rank.getIcon());

            // Draw the rank name in smaller font (using scale factor)
            float scale = 0.75f; // 75% of normal size
            context.getMatrices().push();
            context.getMatrices().scale(scale, scale, 1.0f);
            float scaledX = (leftMargin + 8 + iconWidth) / scale; // Position right after icon
            float scaledY = (playerY + 6) / scale; // Adjust for scaling
            context.drawTextWithShadow(this.textRenderer, rank.getDisplayName(),
                (int)scaledX, (int)scaledY, rank.getColor());
            context.getMatrices().pop();

            // Draw player name (closer to left)
            String playerName = player.getName();
            if (client.player != null && playerName.equals(client.player.getName().getString())) {
                playerName += " (You)";
            }
            context.drawTextWithShadow(this.textRenderer, playerName,
                leftMargin + rankWidth + 5, playerY + 5, 0xFFFFFF);

            // Draw online status - more compact and closer to left
            String statusText = player.isOnline() ? "Online" : "Offline";
            int statusColor = player.isOnline() ? 0x55FF55 : 0xAAAAAA;
            context.drawTextWithShadow(this.textRenderer, statusText,
                leftMargin + rankWidth + nameWidth + 5, playerY + 5, statusColor);

            // Draw action buttons - more compact
            // Show info button for all players
            int infoX = leftMargin + rankWidth + nameWidth + statusWidth + 5; // Closer to left
            int infoY = playerY + 1; // Moved up slightly
            int infoButtonWidth = 35; // Narrower for two buttons
            int infoButtonHeight = 16;

            boolean infoHovered = mouseX >= infoX && mouseX <= infoX + infoButtonWidth &&
                                 mouseY >= infoY && mouseY <= infoY + infoButtonHeight;

            drawModernButton(context, infoX, infoY, infoButtonWidth, infoButtonHeight, 0xFF4CAF50, // Green color
                infoHovered, true);
            context.drawCenteredTextWithShadow(this.textRenderer, "Info",
                infoX + infoButtonWidth / 2, infoY + 4, 0xFFFFFF);

            // Check if there's an active election
            boolean hasElection = false;
            if (playerTown != null) {
                Election election = ElectionManager.getInstance().getElection(playerTown);
                hasElection = (election != null);
            }

            // Only show manage/vote button for non-owner players and if current player has permission
            boolean canManagePlayers = false;
            if (client.player != null && playerTown != null) {
                TownPlayer currentTownPlayer = playerTown.getPlayer(client.player.getUuid());
                if (currentTownPlayer != null) {
                    // Check if player is owner (always has all permissions) or has the specific permission
                    canManagePlayers = currentTownPlayer.getRank() == TownPlayerRank.OWNER ||
                                       currentTownPlayer.hasPermission("Player Management", "Can manage player permissions");
                }
            }

            if (canManagePlayers && rank != TownPlayerRank.OWNER) {
                int manageX = infoX + infoButtonWidth + 5; // Position after info button
                int manageY = playerY + 1;
                int manageButtonWidth = hasElection ? 70 : 45; // Wider for "Vote Mayor"
                int manageButtonHeight = 16;

                boolean manageHovered = mouseX >= manageX && mouseX <= manageX + manageButtonWidth &&
                                       mouseY >= manageY && mouseY <= manageY + manageButtonHeight;

                // Use different colors for different buttons
                int buttonColor = hasElection ? 0xFF4CAF50 : 0xFF2196F3; // Green for vote, Blue for manage

                drawModernButton(context, manageX, manageY, manageButtonWidth, manageButtonHeight, buttonColor,
                    manageHovered, true);

                // Draw different text based on election status
                String buttonText = hasElection ? "Vote Mayor" : "Manage";
                context.drawCenteredTextWithShadow(this.textRenderer, buttonText,
                    manageX + manageButtonWidth / 2, manageY + 4, 0xFFFFFF);
            }

            playerY += rowHeight;
            visiblePlayers++;
        }
    }

    /**
     * Renders the Claims subcategory content.
     */
    private void renderClaimsSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Layout variables - more compact
        int leftMargin = contentX + 15;
        int rowHeight = 14; // Reduced from 16 to 14
        int rowSpacing = 5; // Reduced from 8 to 5
        int buttonWidth = 110; // Reduced from 120 to 110
        int buttonHeight = 18; // Reduced from 20 to 18
        int currentY = contentY + 10; // Reduced from 15 to 10

        // Draw claims title
        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Claims").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);
        currentY += rowHeight + 3; // Reduced from 5 to 3

        // Draw claim usage information
        // Get actual claim usage from the town
        int usedClaims = playerTown.getClaimCount();
        int maxClaims = playerTown.getMaxClaims();
        int availableClaims = maxClaims - usedClaims; // Calculate available claims correctly

        // Draw claim usage text - more compact with two columns
        context.drawTextWithShadow(this.textRenderer, Text.literal("Claim Usage:").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);
        currentY += rowHeight;

        // Two-column layout for claim usage
        int col1X = leftMargin + 5;
        int col2X = leftMargin + contentWidth/2 - 30;

        // Draw used claims in column 1
        context.drawTextWithShadow(this.textRenderer, "Used:",
            col1X, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, usedClaims + "/" + maxClaims,
            col1X + 40, currentY, 0xFFFFFF);

        // Draw available claims in column 2
        context.drawTextWithShadow(this.textRenderer, "Available:",
            col2X, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, availableClaims + " chunks",
            col2X + 60, currentY, 0x55FF55); // Green color for available claims
        currentY += rowHeight + rowSpacing;

        // Draw claim tools section - more compact with two columns
        context.drawTextWithShadow(this.textRenderer, Text.literal("Claim Tools:").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);
        currentY += rowHeight + 3; // Reduced spacing

        // Two-column layout for buttons
        int buttonSpacing = 5;
        int smallerButtonWidth = (contentWidth - 60) / 2;

        // Row 1: Claim Tool and View Boundaries
        // Draw claim tool button (left column)
        int claimToolX = leftMargin + 5;
        int claimToolY = currentY;
        boolean claimToolHovered = mouseX >= claimToolX && mouseX <= claimToolX + smallerButtonWidth &&
                                  mouseY >= claimToolY && mouseY <= claimToolY + buttonHeight;
        drawModernButton(context, claimToolX, claimToolY, smallerButtonWidth, buttonHeight, 0xFF4CAF50, // Green
            claimToolHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Claim Tool",
            claimToolX + smallerButtonWidth / 2, claimToolY + 5, 0xFFFFFF);

        // Draw view boundaries button (right column)
        int viewBoundariesX = leftMargin + smallerButtonWidth + buttonSpacing + 5;
        int viewBoundariesY = currentY;
        boolean viewBoundariesHovered = mouseX >= viewBoundariesX && mouseX <= viewBoundariesX + smallerButtonWidth &&
                                       mouseY >= viewBoundariesY && mouseY <= viewBoundariesY + buttonHeight;
        drawModernButton(context, viewBoundariesX, viewBoundariesY, smallerButtonWidth, buttonHeight, 0xFF2196F3, // Blue
            viewBoundariesHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "View Boundaries",
            viewBoundariesX + smallerButtonWidth / 2, viewBoundariesY + 5, 0xFFFFFF);
        currentY += buttonHeight + 4; // Reduced spacing

        // Row 2: Claim Settings and Remove All Claims
        // Draw claim settings button (left column)
        int claimSettingsX = leftMargin + 5;
        int claimSettingsY = currentY;
        boolean claimSettingsHovered = mouseX >= claimSettingsX && mouseX <= claimSettingsX + smallerButtonWidth &&
                                      mouseY >= claimSettingsY && mouseY <= claimSettingsY + buttonHeight;
        drawModernButton(context, claimSettingsX, claimSettingsY, smallerButtonWidth, buttonHeight, 0xFF9C27B0, // Purple
            claimSettingsHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Tag Settings",
            claimSettingsX + smallerButtonWidth / 2, claimSettingsY + 5, 0xFFFFFF);

        // Draw remove all claims button (right column)
        int removeClaimsX = leftMargin + smallerButtonWidth + buttonSpacing + 5;
        int removeClaimsY = currentY;
        boolean removeClaimsHovered = mouseX >= removeClaimsX && mouseX <= removeClaimsX + smallerButtonWidth &&
                                     mouseY >= removeClaimsY && mouseY <= removeClaimsY + buttonHeight;
        drawModernButton(context, removeClaimsX, removeClaimsY, smallerButtonWidth, buttonHeight, 0xFFE53935, // Red
            removeClaimsHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Remove All Claims",
            removeClaimsX + smallerButtonWidth / 2, removeClaimsY + 5, 0xFFFFFF);
        currentY += buttonHeight + rowSpacing;

        // Draw claim history section with more info button
        context.drawTextWithShadow(this.textRenderer, Text.literal("Claim History:").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);

        // Draw "More Info" button
        int moreInfoWidth = 60;
        int moreInfoHeight = 14;
        int moreInfoX = leftMargin + 120; // Position near the title
        int moreInfoY = currentY - 2; // Align with the Claim History title

        boolean moreInfoHovered = mouseX >= moreInfoX && mouseX <= moreInfoX + moreInfoWidth &&
                                 mouseY >= moreInfoY && mouseY <= moreInfoY + moreInfoHeight;

        // Draw button background
        drawModernButton(context, moreInfoX, moreInfoY, moreInfoWidth, moreInfoHeight, 0xFF4B69FF, moreInfoHovered, true);

        // Draw button text
        context.drawCenteredTextWithShadow(this.textRenderer, "More Info", moreInfoX + moreInfoWidth / 2, moreInfoY + 3, 0xFFFFFF);

        currentY += rowHeight + 2; // Reduced from 5 to 2

        // Get real claim history from the town
        List<ClaimHistoryEntry> claimHistory = playerTown.getClaimHistory();

        // If history is empty, show a message
        if (claimHistory.isEmpty()) {
            // Draw entry background
            context.fill(leftMargin, currentY, leftMargin + contentWidth - 30, currentY + rowHeight, 0x20FFFFFF);

            // Draw message
            context.drawTextWithShadow(this.textRenderer, "No claim history available",
                leftMargin + 10, currentY + 4, 0xAAAAAA);
            currentY += rowHeight;

            // Draw a hint about how to create history
            context.fill(leftMargin, currentY, leftMargin + contentWidth - 30, currentY + rowHeight, 0x10FFFFFF);
            context.drawTextWithShadow(this.textRenderer, "Use the claim tool to create history",
                leftMargin + 10, currentY + 4, 0x888888);
            currentY += rowHeight;
        } else {
            // Draw claim history entries (show up to 5 most recent entries)
            int maxEntries = Math.min(5, claimHistory.size());

            for (int i = 0; i < maxEntries; i++) {
                // Skip if we're out of space
                if (currentY + rowHeight > contentY + contentHeight - 10) {
                    break;
                }

                ClaimHistoryEntry entry = claimHistory.get(i);

                // Draw entry background (alternating colors)
                int bgColor = i % 2 == 0 ? 0x20FFFFFF : 0x30FFFFFF;
                context.fill(leftMargin, currentY, leftMargin + contentWidth - 30, currentY + rowHeight, bgColor);

                // Determine color based on action
                int actionColor;
                switch (entry.getAction()) {
                    case CLAIM:
                        actionColor = 0x55FF55; // Green
                        break;
                    case UNCLAIM:
                        actionColor = 0xFF5555; // Red
                        break;
                    case MODIFY:
                        actionColor = 0xFFAA00; // Orange
                        break;
                    default:
                        actionColor = 0xFFFFFF; // White
                }

                // Draw action text
                String actionText = entry.getShortDescription();
                context.drawTextWithShadow(this.textRenderer, actionText,
                    leftMargin + 10, currentY + 4, actionColor);

                // Draw time text
                String timeText = entry.getFormattedTimestamp();
                int timeWidth = this.textRenderer.getWidth(timeText);
                context.drawTextWithShadow(this.textRenderer, timeText,
                    leftMargin + contentWidth - 40 - timeWidth, currentY + 4, 0xAAAAAA);

                currentY += rowHeight;
            }

            // If there are more entries than we can show, add a message
            if (claimHistory.size() > maxEntries) {
                // Draw entry background
                context.fill(leftMargin, currentY, leftMargin + contentWidth - 30, currentY + rowHeight, 0x20FFFFFF);

                // Draw message
                String moreText = "... and " + (claimHistory.size() - maxEntries) + " more entries";
                context.drawTextWithShadow(this.textRenderer, moreText,
                    leftMargin + 10, currentY + 4, 0xAAAAAA);

                currentY += rowHeight;
            }
        }
    }

    /**
     * Checks if town data synchronization appears to be complete.
     * This helps avoid showing incorrect rank data before synchronization finishes.
     *
     * @param town The town to check
     * @return true if synchronization appears complete, false otherwise
     */
    private boolean isTownDataSynchronized(Town town) {
        if (town == null) {
            return false;
        }

        // Check if we have TownPlayer objects or rank data for most players in the town
        List<UUID> townPlayerIds = town.getPlayers();
        if (townPlayerIds.isEmpty()) {
            return true; // Empty town is considered synchronized
        }

        int playersWithData = 0;
        for (UUID playerId : townPlayerIds) {
            TownPlayer townPlayer = town.getPlayer(playerId);
            TownPlayerRank rank = town.getPlayerRank(playerId);
            if (townPlayer != null || rank != null) {
                playersWithData++;
            }
        }

        // Consider synchronized if we have data for at least 80% of players
        double synchronizationRatio = (double) playersWithData / townPlayerIds.size();
        return synchronizationRatio >= 0.8;
    }

    /**
     * Creates a list of players for the town.
     * In multiplayer, uses real player data.
     * In singleplayer, generates dummy players for testing.
     * Uses caching to prevent regeneration on every frame.
     */
    private List<TownPlayer> createDummyPlayerList(Town town) {
        // Always get fresh player ranks during elections
        boolean hasElection = false;
        if (town != null) {
            Election election = ElectionManager.getInstance().getElection(town);
            hasElection = (election != null || town.isInElection());
        }

        // Return cached list if the town hasn't changed and no sort/filter/search options changed
        // But always refresh during elections to get the latest ranks
        if (!hasElection && town != null && lastTownId != null && town.getId().equals(lastTownId) && cachedPlayerList != null) {
            return cachedPlayerList;
        }

        // Create a new list if cache is invalid
        List<TownPlayer> players = new ArrayList<>();

        if (town == null) {
            return players;
        }

        // Check if we're in a multiplayer environment
        boolean isMultiplayer = client.getNetworkHandler() != null && client.getNetworkHandler().getPlayerList() != null;

        // Check if town data synchronization is complete in multiplayer
        if (isMultiplayer && !isTownDataSynchronized(town)) {
            // In multiplayer, if synchronization isn't complete, return empty list to avoid showing incorrect data
            com.pokecobble.Pokecobbleclaim.LOGGER.debug("Town data synchronization incomplete for town " + town.getName() + ", waiting for complete data");
            return players; // Return empty list
        }

        // Get the list of player UUIDs in the town
        List<UUID> townPlayerIds = town.getPlayers();

        if (isMultiplayer) {
            // We're in multiplayer - use real player data

            // Add current player with their actual rank from the town
            if (client.player != null && townPlayerIds.contains(client.player.getUuid())) {
                TownPlayerRank currentPlayerRank = town.getPlayerRank(client.player.getUuid());
                if (currentPlayerRank == null) {
                    // For the current player, we should always have rank data
                    // If not, it means synchronization hasn't completed yet
                    com.pokecobble.Pokecobbleclaim.LOGGER.warn("Current player rank not found in town data - synchronization may be incomplete");
                    currentPlayerRank = TownPlayerRank.MEMBER; // Temporary fallback
                }
                players.add(new TownPlayer(client.player.getUuid(), client.player.getName().getString(), currentPlayerRank, true));
            }

            // Add other players from the town data (both online and offline)
            for (UUID playerId : townPlayerIds) {
                // Skip the current player as we've already added them
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                // Get the TownPlayer object which should contain the correct name and data
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null) {
                    // Use the TownPlayer data directly - it contains the correct name and online status
                    players.add(new TownPlayer(townPlayer.getUuid(), townPlayer.getName(),
                                             townPlayer.getRank(), townPlayer.isOnline()));
                } else {
                    // Get the actual rank from the town - this should be available even if TownPlayer object isn't
                    TownPlayerRank rank = town.getPlayerRank(playerId);

                    if (rank != null) {
                        // We have rank data, try to get player name
                        net.minecraft.client.network.PlayerListEntry playerEntry = client.getNetworkHandler().getPlayerListEntry(playerId);
                        if (playerEntry != null) {
                            // This is an online player
                            String playerName = playerEntry.getProfile().getName();
                            players.add(new TownPlayer(playerId, playerName, rank, true));
                        } else {
                            // Offline player - use stored name if available, otherwise "Unknown Player"
                            String playerName = town.getPlayerName(playerId);
                            if (playerName.equals("Unknown Player")) {
                                // Try to get name from player data storage
                                try {
                                    com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData playerData =
                                        com.pokecobble.town.data.PlayerDataStorage.loadPlayerData(playerId);
                                    if (playerData != null && playerData.getPlayerName() != null) {
                                        playerName = playerData.getPlayerName();
                                    }
                                } catch (Exception e) {
                                    com.pokecobble.Pokecobbleclaim.LOGGER.debug("Could not load player name for " + playerId + ": " + e.getMessage());
                                }
                            }
                            players.add(new TownPlayer(playerId, playerName, rank, false));
                        }
                    } else {
                        // No rank data available - this indicates incomplete synchronization
                        // Skip this player for now rather than showing incorrect data
                        com.pokecobble.Pokecobbleclaim.LOGGER.warn("Player " + playerId + " in town but no rank data available - skipping until synchronization completes");
                    }
                }
            }
        } else {
            // We're in singleplayer - use real player data like in multiplayer

            // Add other players from the town data (both online and offline)
            for (UUID playerId : townPlayerIds) {
                // Skip the current player as we've already added them
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                // Get the TownPlayer object which should contain the correct name and data
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null) {
                    // Use the TownPlayer data directly - it contains the correct name and online status
                    players.add(new TownPlayer(townPlayer.getUuid(), townPlayer.getName(),
                                             townPlayer.getRank(), townPlayer.isOnline()));
                } else {
                    // Get the actual rank from the town - this should be available
                    TownPlayerRank rank = town.getPlayerRank(playerId);

                    if (rank != null) {
                        // Use stored name if available, otherwise "Unknown Player"
                        String playerName = town.getPlayerName(playerId);
                        if (playerName.equals("Unknown Player")) {
                            // Try to get name from player data storage
                            try {
                                com.pokecobble.town.data.PlayerDataStorage.SerializablePlayerData playerData =
                                    com.pokecobble.town.data.PlayerDataStorage.loadPlayerData(playerId);
                                if (playerData != null && playerData.getPlayerName() != null) {
                                    playerName = playerData.getPlayerName();
                                }
                            } catch (Exception e) {
                                com.pokecobble.Pokecobbleclaim.LOGGER.debug("Could not load player name for " + playerId + ": " + e.getMessage());
                            }
                        }
                        // In singleplayer, assume players are offline unless they're the current player
                        players.add(new TownPlayer(playerId, playerName, rank, false));
                    } else {
                        // No rank data available - skip this player
                        com.pokecobble.Pokecobbleclaim.LOGGER.warn("Player " + playerId + " in town but no rank data available in singleplayer");
                    }
                }
            }

            // Add current player with their actual rank from the town
            if (client.player != null && townPlayerIds.contains(client.player.getUuid())) {
                TownPlayerRank currentPlayerRank = town.getPlayerRank(client.player.getUuid());
                if (currentPlayerRank == null) {
                    currentPlayerRank = TownPlayerRank.OWNER; // Default to owner in singleplayer if no data
                }
                players.add(new TownPlayer(client.player.getUuid(), client.player.getName().getString(), currentPlayerRank, true));
            }
        }

        // Apply filtering based on current filter type
        List<TownPlayer> filteredPlayers = new ArrayList<>(players);
        if (currentFilterType == FilterType.ONLINE) {
            filteredPlayers.removeIf(player -> !player.isOnline());
        } else if (currentFilterType == FilterType.OFFLINE) {
            filteredPlayers.removeIf(TownPlayer::isOnline);
        }

        // Apply search if there's a query
        if (!searchQuery.isEmpty()) {
            filteredPlayers.removeIf(player -> !player.getName().toLowerCase().contains(searchQuery.toLowerCase()));
        }

        // Apply sorting based on current sort type
        switch (currentSortType) {
            case RANK:
                // Sort by rank (Owner > Deputy > Council > Resident > Citizen)
                // Use a completely different approach with explicit rank ordering

                // Create separate lists for each rank
                List<TownPlayer> owners = new ArrayList<>();
                List<TownPlayer> deputies = new ArrayList<>();
                List<TownPlayer> council = new ArrayList<>();
                List<TownPlayer> residents = new ArrayList<>();
                List<TownPlayer> citizens = new ArrayList<>();

                // Categorize players by rank
                for (TownPlayer player : filteredPlayers) {
                    switch (player.getRank()) {
                        case OWNER:
                            owners.add(player);
                            break;
                        case ADMIN:
                            deputies.add(player);
                            break;
                        case MODERATOR:
                            council.add(player);
                            break;
                        case MEMBER:
                            residents.add(player);
                            break;
                        case VISITOR:
                            citizens.add(player);
                            break;
                    }
                }

                // Sort each list by name
                Comparator<TownPlayer> nameComparator = Comparator.comparing(TownPlayer::getName, String::compareToIgnoreCase);
                Collections.sort(owners, nameComparator);
                Collections.sort(deputies, nameComparator);
                Collections.sort(council, nameComparator);
                Collections.sort(residents, nameComparator);
                Collections.sort(citizens, nameComparator);

                // Clear the original list and add players in order of rank
                filteredPlayers.clear();
                filteredPlayers.addAll(owners);
                filteredPlayers.addAll(deputies);
                filteredPlayers.addAll(council);
                filteredPlayers.addAll(residents);
                filteredPlayers.addAll(citizens);
                break;
            case NAME:
                // Sort by name alphabetically
                filteredPlayers.sort((p1, p2) -> p1.getName().compareToIgnoreCase(p2.getName()));
                break;
            case NEWEST:
                // For demo purposes, we'll use the UUID's least significant bits as a proxy for "join time"
                filteredPlayers.sort((p1, p2) -> Long.compare(p2.getUuid().getLeastSignificantBits(), p1.getUuid().getLeastSignificantBits()));
                break;
            case OLDEST:
                // For demo purposes, we'll use the UUID's least significant bits as a proxy for "join time"
                filteredPlayers.sort((p1, p2) -> Long.compare(p1.getUuid().getLeastSignificantBits(), p2.getUuid().getLeastSignificantBits()));
                break;
        }

        // Update cache with the filtered and sorted list
        if (town != null) {
            lastTownId = town.getId();
            cachedPlayerList = null; // Force regeneration when sort/filter changes
        }

        return filteredPlayers;
    }

    /**
     * Renders the Level subcategory content.
     */
    private void renderLevelSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Layout variables
        int leftMargin = contentX + 15;
        int rowHeight = 12; // Even more compact row height
        int sectionSpacing = 6; // Further reduced spacing between sections
        int currentY = contentY + 8; // Start even closer to the top

        // Get town level (for demo purposes)
        int townLevel = 3; // Example level
        int currentExp = 750; // Example current experience
        int expForNextLevel = 1000; // Example experience needed for next level

        // Draw level title and progress on the same line
        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Level").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);

        // Draw current level and progress percentage on the same line
        String levelText = "Lvl " + townLevel + " (" + (currentExp * 100 / expForNextLevel) + "%)";
        int levelTextWidth = this.textRenderer.getWidth(levelText);
        context.drawTextWithShadow(this.textRenderer, levelText,
            leftMargin + contentWidth - 60 - levelTextWidth, currentY, 0xFFFFFF);
        currentY += rowHeight + 2;

        // Draw progress bar background
        int barWidth = contentWidth - 60;
        int barHeight = 6; // Even smaller height
        int barX = leftMargin;
        int barY = currentY;
        context.fill(barX, barY, barX + barWidth, barY + barHeight, 0x80000000);

        // Draw progress bar fill
        int fillWidth = (int)((float)currentExp / expForNextLevel * barWidth);
        context.fill(barX, barY, barX + fillWidth, barY + barHeight, 0xFFFFAA00);

        // Draw progress bar border
        context.drawBorder(barX, barY, barWidth, barHeight, 0xFFFFFFFF);
        currentY += barHeight + 6; // Further reduced spacing

        // Draw next level benefits section with title on same line as first benefit
        int nextLevel = townLevel + 1;

        // Show only the next level benefits
        String[][] allBenefits = {
            {"Basic town features", "Town chat", "Town spawn point"},
            {"Custom town banner", "Town welcome message", "Town shop discounts"},
            {"Town teleport point", "Town protection", "Town storage"},
            {"Custom building styles", "Town weather control", "Town farm boost"},
            {"Town aura effects", "Town flight", "Town special events"}
        };

        // Check if next level exists
        if (nextLevel <= 5) {
            String[] nextLevelBenefits = allBenefits[nextLevel - 1];

            // Draw next level title and first benefit on the same line
            context.drawTextWithShadow(this.textRenderer, Text.literal("Next Level:").formatted(Formatting.BOLD),
                leftMargin, currentY, 0xFFFFFF);
            context.drawTextWithShadow(this.textRenderer, "Lvl " + nextLevel,
                leftMargin + 70, currentY, 0xFFFFAA00);
            currentY += rowHeight;

            // Draw the 3 benefits in a more compact way
            int benefitX = leftMargin + 10;
            for (int i = 0; i < nextLevelBenefits.length; i++) {
                context.drawTextWithShadow(this.textRenderer, "• " + nextLevelBenefits[i],
                    benefitX, currentY, 0xFFFFFF);
                currentY += rowHeight - 1; // Slightly overlap rows
            }
        } else {
            // Max level reached
            context.drawTextWithShadow(this.textRenderer, Text.literal("Next Level:").formatted(Formatting.BOLD),
                leftMargin, currentY, 0xFFFFFF);
            context.drawTextWithShadow(this.textRenderer, "Maximum level reached!",
                leftMargin + 70, currentY, 0x55FF55);
            currentY += rowHeight;
        }

        // Add "See All Benefits" button - smaller and more compact
        int seeAllButtonWidth = 90;
        int seeAllButtonHeight = 14;
        int seeAllButtonX = leftMargin;
        int seeAllButtonY = currentY + 1;
        boolean seeAllHovered = mouseX >= seeAllButtonX && mouseX <= seeAllButtonX + seeAllButtonWidth &&
                               mouseY >= seeAllButtonY && mouseY <= seeAllButtonY + seeAllButtonHeight;
        drawModernButton(context, seeAllButtonX, seeAllButtonY, seeAllButtonWidth, seeAllButtonHeight, 0xFF2196F3, seeAllHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "See All Benefits",
            seeAllButtonX + seeAllButtonWidth / 2, seeAllButtonY + 3, 0xFFFFFF);

        currentY += seeAllButtonHeight + sectionSpacing - 2; // Further reduced spacing

        // Add "Contribute" button
        int contributeButtonWidth = 100;
        int contributeButtonHeight = 16;
        int contributeButtonX = leftMargin;
        int contributeButtonY = currentY;
        boolean contributeHovered = mouseX >= contributeButtonX && mouseX <= contributeButtonX + contributeButtonWidth &&
                                   mouseY >= contributeButtonY && mouseY <= contributeButtonY + contributeButtonHeight;
        drawModernButton(context, contributeButtonX, contributeButtonY, contributeButtonWidth, contributeButtonHeight, 0xFF9C27B0, contributeHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Contribute Coins",
            contributeButtonX + contributeButtonWidth / 2, contributeButtonY + 4, 0xFFFFFF);

        currentY += contributeButtonHeight + 6; // Reduced spacing

        // Draw top contributors section - even more compact
        currentY += 5; // Small spacing

        // Draw top contributors title and first contributor on the same line
        context.drawTextWithShadow(this.textRenderer, Text.literal("Top:").formatted(Formatting.BOLD),
            leftMargin, currentY + 1, 0xFFFFFF);

        // Draw first contributor on the same line as the title
        context.drawTextWithShadow(this.textRenderer, "1. Player1: 5000",
            leftMargin + 35, currentY + 1, 0xFFFFFF);
        currentY += rowHeight - 1; // Slightly overlap rows

        // Calculate positions for compact layout
        int column1X = leftMargin + 5;
        int column2X = leftMargin + contentWidth/2 - 20;

        // Draw remaining contributors in a compact grid (2x2)
        String[][] contributors = {
            {"2. Player2: 3500", "4. You: 1500"},
            {"3. Player3: 2000", "5. Player5: 1000"}
        };

        for (int row = 0; row < 2; row++) {
            for (int col = 0; col < 2; col++) {
                int color = (row == 1 && col == 1) ? 0xFFFFFF : (row == 0 && col == 1) ? 0xFFFF55 : 0xFFFFFF;
                int x = (col == 0) ? column1X : column2X;
                context.drawTextWithShadow(this.textRenderer, contributors[row][col],
                    x, currentY + (row * (rowHeight - 1)), color);
            }
        }
    }

    /**
     * Renders the Bank subcategory content.
     */
    private void renderBankSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // More compact layout
        int leftMargin = contentX + 15;
        int rowHeight = 16; // Smaller row height
        int sectionSpacing = 15; // Spacing between sections
        int currentY = contentY + 15;

        // Draw bank title
        context.drawTextWithShadow(this.textRenderer, Text.literal("Town Bank").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);
        currentY += rowHeight + 5;

        // Draw current balance with gold color
        int balance = 1000; // Placeholder value
        String balanceText = "Balance: $" + balance;
        context.drawTextWithShadow(this.textRenderer, Text.literal(balanceText).formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFD700); // Gold color
        currentY += rowHeight + sectionSpacing;

        // Draw transaction history in a compact list
        context.drawTextWithShadow(this.textRenderer, Text.literal("Recent Transactions:").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);
        currentY += rowHeight;

        // Calculate how many transactions can fit
        int transactionListY = currentY;
        int maxVisibleTransactions = Math.min(8, (contentY + contentHeight - 60 - transactionListY) / rowHeight);

        // Draw placeholder transaction history
        String[] transactions = {
            "• Deposit: +$500 (You)",
            "• Withdrawal: -$200 (Player1)",
            "• Deposit: +$300 (Player2)",
            "• Withdrawal: -$100 (You)",
            "• Deposit: +$250 (Player3)",
            "• Withdrawal: -$150 (Player2)",
            "• Deposit: +$400 (You)",
            "• Withdrawal: -$50 (Player1)"
        };

        for (int i = 0; i < maxVisibleTransactions; i++) {
            if (i < transactions.length) {
                int color = transactions[i].contains("+$") ? 0x55FF55 : 0xFF5555;
                context.drawTextWithShadow(this.textRenderer, transactions[i],
                    leftMargin + 10, transactionListY + (i * rowHeight), color);
            }
        }

        // Draw deposit/withdraw buttons at the bottom
        int buttonY = contentY + 170; // Fixed position for buttons
        int buttonWidth = 80; // Smaller buttons
        int buttonHeight = 20;
        int buttonSpacing = 10;

        // Deposit button
        int depositX = leftMargin;
        drawModernButton(context, depositX, buttonY, buttonWidth, buttonHeight, 0xFF4CAF50,
            mouseX >= depositX && mouseX <= depositX + buttonWidth && mouseY >= buttonY && mouseY <= buttonY + buttonHeight, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Deposit", depositX + buttonWidth / 2, buttonY + 6, 0xFFFFFF);

        // Withdraw button
        int withdrawX = depositX + buttonWidth + buttonSpacing;
        drawModernButton(context, withdrawX, buttonY, buttonWidth, buttonHeight, 0xFFE53935,
            mouseX >= withdrawX && mouseX <= withdrawX + buttonWidth && mouseY >= buttonY && mouseY <= buttonY + buttonHeight, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Withdraw", withdrawX + buttonWidth / 2, buttonY + 6, 0xFFFFFF);
    }

    /**
     * Renders the Election subcategory content.
     */
    private void renderElectionSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Get the election from client-side manager
        Election election = com.pokecobble.town.client.ClientElectionManager.getInstance().getElection(playerTown.getId());
        if (election == null) {
            // No election in progress
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("No election is currently in progress.").formatted(Formatting.ITALIC),
                    contentX + contentWidth / 2, contentY + contentHeight / 2, 0xFFFFFF);
            return;
        }

        // Layout variables
        int leftMargin = contentX + 15;
        int rowHeight = 18; // Same as players subcategory
        int currentY = contentY + 10; // Same as players subcategory

        // Draw election title and timer
        context.drawTextWithShadow(this.textRenderer, Text.literal("Mayoral Election").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);

        // Draw election timer on the right
        long remainingTime = election.getRemainingTime();
        long hours = remainingTime / (60 * 60 * 1000);
        long minutes = (remainingTime % (60 * 60 * 1000)) / (60 * 1000);
        String timeText = "Time remaining: " + hours + "h " + minutes + "m";
        int timeWidth = this.textRenderer.getWidth(timeText);
        context.drawTextWithShadow(this.textRenderer,
                Text.literal(timeText).formatted(Formatting.GOLD),
                contentX + contentWidth - 15 - timeWidth, currentY, 0xFFFFFF);
        currentY += rowHeight;

        // Draw column headers - similar to players subcategory but with votes instead of status
        int rankWidth = 50; // Same as players subcategory
        int nameWidth = 130; // Same as players subcategory
        int votesWidth = 45; // Instead of status
        int actionWidth = 45; // Same as players subcategory

        // Draw column headers
        context.drawTextWithShadow(this.textRenderer, Text.literal("Rank").formatted(Formatting.BOLD),
            leftMargin + 5, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Name").formatted(Formatting.BOLD),
            leftMargin + rankWidth + 5, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Votes").formatted(Formatting.BOLD),
            leftMargin + rankWidth + nameWidth + 5, currentY, 0xAAAAAA);
        context.drawTextWithShadow(this.textRenderer, Text.literal("Action").formatted(Formatting.BOLD),
            leftMargin + rankWidth + nameWidth + votesWidth + 5, currentY, 0xAAAAAA);

        // Move currentY down to account for the header row
        currentY += rowHeight;

        // Calculate list area dimensions
        int listAreaY = currentY;
        int listAreaHeight = contentHeight - 20 - rowHeight - rowHeight; // Adjust height to account for title/timer and column headers

        // Draw list area background
        context.fill(leftMargin, listAreaY, leftMargin + contentWidth - 30, listAreaY + listAreaHeight, 0x20000000);

        // Get candidates and vote counts
        List<UUID> sortedCandidates = election.getSortedCandidates();
        Map<UUID, Integer> voteCount = election.getVoteCount();

        // Check if player has already voted
        boolean hasVoted = false;
        UUID votedFor = null;

        if (client.player != null) {
            UUID playerId = client.player.getUuid();
            hasVoted = election.hasVoted(playerId);
            votedFor = election.getVotes().get(playerId);
        }

        // Create a list of TownPlayer objects for the candidates
        List<TownPlayer> candidates = new ArrayList<>();
        for (UUID candidateId : sortedCandidates) {
            // Get player name
            String playerName = getPlayerName(candidateId);

            // Get player rank
            TownPlayerRank rank = playerTown.getPlayerRank(candidateId);
            if (rank == null) {
                rank = TownPlayerRank.MEMBER; // Default to member if rank not found
            }

            // Create TownPlayer object
            TownPlayer candidate = new TownPlayer(candidateId, playerName, rank, true);
            candidates.add(candidate);
        }

        // Calculate total height of all candidates
        int totalHeight = candidates.size() * rowHeight;

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - listAreaHeight);
        electionScrollOffset = Math.min(electionScrollOffset, maxScroll);

        // Draw scrollbar if needed
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(leftMargin + contentWidth - 25, listAreaY, leftMargin + contentWidth - 22, listAreaY + listAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(20, listAreaHeight * listAreaHeight / totalHeight);
            int scrollbarY = listAreaY + (listAreaHeight - scrollbarHeight) * electionScrollOffset / maxScroll;

            // Draw scrollbar handle
            context.fill(leftMargin + contentWidth - 25, scrollbarY, leftMargin + contentWidth - 22, scrollbarY + scrollbarHeight, 0x80FFFFFF);
        }

        // Draw candidate list with scrolling
        int candidateY = listAreaY - electionScrollOffset;
        int visibleCandidates = 0;

        for (TownPlayer candidate : candidates) {
            // Skip if candidate is completely outside visible area
            if (candidateY + rowHeight < listAreaY || candidateY > listAreaY + listAreaHeight) {
                candidateY += rowHeight;
                continue;
            }

            // Draw candidate entry background (alternating colors)
            int bgColor = visibleCandidates % 2 == 0 ? 0x20FFFFFF : 0x30FFFFFF;
            context.fill(leftMargin, candidateY, leftMargin + contentWidth - 30, candidateY + rowHeight, bgColor);

            // Draw rank icon and name with color
            TownPlayerRank rank = candidate.getRank();
            // Draw rank icon
            context.drawTextWithShadow(this.textRenderer, rank.getIcon(),
                leftMargin + 5, candidateY + 5, rank.getColor());
            // Draw rank name
            context.drawTextWithShadow(this.textRenderer, getRankText(rank),
                leftMargin + 15, candidateY + 5, rank.getColor());

            // Draw player name
            context.drawTextWithShadow(this.textRenderer, candidate.getName(),
                leftMargin + rankWidth + 5, candidateY + 5, 0xFFFFFF);

            // Draw vote count
            int votes = voteCount.getOrDefault(candidate.getUuid(), 0);
            context.drawTextWithShadow(this.textRenderer, String.valueOf(votes),
                leftMargin + rankWidth + nameWidth + 5, candidateY + 5, 0xFFFFFF);

            // Draw info button
            int infoX = leftMargin + rankWidth + nameWidth + votesWidth + 5;
            int infoY = candidateY + 1;
            int infoButtonWidth = 30;
            int infoButtonHeight = 16;
            boolean infoHovered = mouseX >= infoX && mouseX <= infoX + infoButtonWidth &&
                                 mouseY >= infoY && mouseY <= infoY + infoButtonHeight;

            drawModernButton(context, infoX, infoY, infoButtonWidth, infoButtonHeight, 0xFF2196F3, // Blue
                infoHovered, true);
            context.drawCenteredTextWithShadow(this.textRenderer, "Info",
                infoX + infoButtonWidth / 2, infoY + 4, 0xFFFFFF);

            // Draw vote button (or voted indicator)
            boolean isVotedFor = hasVoted && candidate.getUuid().equals(votedFor);

            if (isVotedFor) {
                // Show voted indicator
                int voteX = infoX + infoButtonWidth + 5;
                int voteY = candidateY + 1;
                int voteButtonWidth = 30;
                int voteButtonHeight = 16;

                drawModernButton(context, voteX, voteY, voteButtonWidth, voteButtonHeight, 0xFF55FF55, // Green
                    false, true);
                context.drawCenteredTextWithShadow(this.textRenderer, "✓",
                    voteX + voteButtonWidth / 2, voteY + 4, 0xFFFFFF);
            } else if (!hasVoted) {
                // Draw vote button
                int voteX = infoX + infoButtonWidth + 5;
                int voteY = candidateY + 1;
                int voteButtonWidth = 30;
                int voteButtonHeight = 16;
                boolean voteHovered = mouseX >= voteX && mouseX <= voteX + voteButtonWidth &&
                                     mouseY >= voteY && mouseY <= voteY + voteButtonHeight;

                drawModernButton(context, voteX, voteY, voteButtonWidth, voteButtonHeight, 0xFF4CAF50, // Green
                    voteHovered, true);
                context.drawCenteredTextWithShadow(this.textRenderer, "Vote",
                    voteX + voteButtonWidth / 2, voteY + 4, 0xFFFFFF);
            }

            candidateY += rowHeight;
            visibleCandidates++;
        }

        // If no candidates, show a message
        if (candidates.isEmpty()) {
            context.drawCenteredTextWithShadow(this.textRenderer,
                    Text.literal("No candidates available.").formatted(Formatting.ITALIC),
                    contentX + contentWidth / 2, listAreaY + 30, 0xFFFFFF);
        }
    }

    /**
     * Gets the name of a player from their UUID.
     *
     * @param playerId The player's UUID
     * @return The player's name, or "Unknown Player" if not found
     */
    private String getPlayerName(UUID playerId) {
        // In a real implementation, this would look up the player's name
        // For now, we'll just return a placeholder
        return "Player " + playerId.toString().substring(0, 8);
    }

    /**
     * Renders the Settings subcategory content.
     */
    private void renderSettingsSubcategory(DrawContext context, int mouseX, int mouseY, Town playerTown, int contentX, int contentY, int contentWidth, int contentHeight) {
        int leftMargin = contentX + 10;
        int rowHeight = 14; // More compact
        int currentY = contentY + 10;

        // Draw settings title
        context.drawTextWithShadow(this.textRenderer, Text.literal("Settings").formatted(Formatting.BOLD),
            leftMargin, currentY, 0xFFFFFF);
        currentY += rowHeight + 8;

        // Compact status display
        int statusY = currentY;

        // Access Status
        String accessIcon = getAccessIcon(playerTown.getJoinType());
        int accessColor = getAccessColor(playerTown.getJoinType());
        context.drawTextWithShadow(this.textRenderer, accessIcon + " Access", leftMargin, statusY, accessColor);
        currentY += rowHeight;

        // Player count
        String playerText = "Players: " + playerTown.getPlayerCount() + "/" + playerTown.getMaxPlayers();
        context.drawTextWithShadow(this.textRenderer, playerText, leftMargin, currentY, 0xCCCCCC);
        currentY += rowHeight + 15;

        // Compact button layout - calculate responsive sizes
        int availableWidth = contentWidth - 20;
        int buttonSpacing = 8;
        int numButtons = 2; // Main action buttons
        int buttonWidth = Math.min(100, (availableWidth - buttonSpacing) / numButtons);
        int buttonHeight = 18;

        // Main action buttons row
        int button1X = leftMargin;
        int button2X = leftMargin + buttonWidth + buttonSpacing;

        // Advanced Settings button
        boolean advancedHovered = mouseX >= button1X && mouseX <= button1X + buttonWidth &&
                                 mouseY >= currentY && mouseY <= currentY + buttonHeight;
        drawModernButton(context, button1X, currentY, buttonWidth, buttonHeight, 0xFF4CAF50, advancedHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Settings",
                button1X + buttonWidth / 2, currentY + 5, 0xFFFFFF);

        // Quick Toggle button
        boolean toggleHovered = mouseX >= button2X && mouseX <= button2X + buttonWidth &&
                               mouseY >= currentY && mouseY <= currentY + buttonHeight;
        int toggleColor = playerTown.getJoinType() == Town.JoinType.OPEN ? 0xFFFF9800 : 0xFF2196F3;
        String toggleText = playerTown.getJoinType() == Town.JoinType.OPEN ? "Close" : "Open";
        drawModernButton(context, button2X, currentY, buttonWidth, buttonHeight, toggleColor, toggleHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, toggleText,
                button2X + buttonWidth / 2, currentY + 5, 0xFFFFFF);

        currentY += buttonHeight + 12;

        // Leave Town button (bottom right, smaller)
        int leaveButtonWidth = 70;
        int leaveButtonHeight = 16;
        int leaveButtonX = contentX + contentWidth - leaveButtonWidth - 10;
        int leaveButtonY = contentY + contentHeight - leaveButtonHeight - 10;
        boolean leaveHovered = mouseX >= leaveButtonX && mouseX <= leaveButtonX + leaveButtonWidth &&
                              mouseY >= leaveButtonY && mouseY <= leaveButtonY + leaveButtonHeight;
        drawModernButton(context, leaveButtonX, leaveButtonY, leaveButtonWidth, leaveButtonHeight, 0xFFE53935, leaveHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Leave",
                leaveButtonX + leaveButtonWidth / 2, leaveButtonY + 4, 0xFFFFFF);
    }

    private String getAccessIcon(Town.JoinType joinType) {
        switch (joinType) {
            case OPEN: return "✓";
            case CLOSED: return "✗";
            case INVITE_ONLY: return "◐";
            default: return "?";
        }
    }

    private int getAccessColor(Town.JoinType joinType) {
        switch (joinType) {
            case OPEN: return 0x55FF55;
            case CLOSED: return 0xFF5555;
            case INVITE_ONLY: return 0x5555FF;
            default: return 0xAAAAAA;
        }
    }

    private void drawCompactButton(DrawContext context, int x, int y, int width, int height, int color, boolean hovered, String text) {
        // Draw button background
        int bgColor = hovered ? (color | 0x20FFFFFF) : color; // Lighter when hovered
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw button border
        int borderColor = hovered ? 0xFFFFFFFF : 0xAAFFFFFF;
        context.fill(x, y, x + width, y + 1, borderColor); // Top
        context.fill(x, y + height - 1, x + width, y + height, borderColor); // Bottom
        context.fill(x, y, x + 1, y + height, borderColor); // Left
        context.fill(x + width - 1, y, x + width, y + height, borderColor); // Right

        // Draw text (centered)
        int textWidth = this.textRenderer.getWidth(text);
        int textX = x + (width - textWidth) / 2;
        int textY = y + (height - this.textRenderer.fontHeight) / 2;
        context.drawTextWithShadow(this.textRenderer, text, textX, textY, 0xFFFFFF);
    }

    /**
     * Renders the town ratings section with 5-star ratings for different categories.
     *
     * @param context The draw context
     * @param mouseX The mouse X position
     * @param mouseY The mouse Y position
     * @param playerTown The player's town
     * @param x The x position to start drawing
     * @param y The y position to start drawing
     * @param width The width of the rating section
     * @param height The height of the rating section
     */
    private void renderTownRatings(DrawContext context, int mouseX, int mouseY, Town playerTown, int x, int y, int width, int height) {
        // Define rating categories
        String[] categories = {
            "Structure",
            "Community",
            "Aesthetics",
            "Economy",
            "Innovation",
            "Activity"
        };

        // Define mock ratings (in a real implementation, these would come from the server)
        // Ratings are from 0 to 5, can be fractional (e.g., 3.5 stars)
        float[] ratings = {
            4.5f, // Structure
            3.0f, // Community
            5.0f, // Aesthetics
            2.5f, // Economy
            4.0f, // Innovation
            3.5f  // Activity
        };

        // Define layout parameters
        int rowHeight = 16;
        int starSize = 12; // Increased size to 12px for better visibility as requested
        int starSpacing = 4; // Fixed spacing of 4px as requested for consistency
        int categoryWidth = 65; // Width allocated for category name

        // Calculate column width based on available space
        // We want to fit two columns with some padding
        int availableWidth = width - 20; // Leave some padding on both sides
        int columnSpacing = 30; // Spacing between columns
        int columnWidth = (availableWidth - columnSpacing) / 2; // Divide available space by 2 columns

        // Calculate number of rows needed (half the categories, rounded up)
        int numRows = (categories.length + 1) / 2;

        // Calculate the total height needed for the ratings section
        int totalHeight = 22 + (numRows * rowHeight) + 20; // Title + rows + footer

        // Make sure we don't exceed the available height
        totalHeight = Math.min(totalHeight, height - 10);

        // Draw a subtle background for the ratings section that fills the entire content area width
        context.fill(x, y, x + width, y + totalHeight, 0x20000000);

        // Title for the ratings section - centered
        Text titleText = Text.literal("Town Ratings").formatted(Formatting.BOLD);
        int titleWidth = this.textRenderer.getWidth(titleText);
        context.drawTextWithShadow(this.textRenderer, titleText,
            x + (width - titleWidth) / 2, y + 5, 0xFFFFFF);
        int startY = y + 22;

        // Draw a subtle separator line that spans the full width
        context.fill(x + 5, startY - 4, x + width - 5, startY - 3, 0x40FFFFFF);

        // Calculate how many categories we can fit in the available space
        int availableHeight = totalHeight - 30; // Subtract space for title and footer
        int maxRows = availableHeight / rowHeight;
        int maxCategories = Math.min(categories.length, maxRows * 2); // Two columns

        for (int i = 0; i < maxCategories; i++) {
            // Determine which column and row this category belongs to
            int column = i % 2; // 0 for left column, 1 for right column
            int row = i / 2;    // Row index

            // Calculate position - evenly distribute columns across the width
            int leftColumnX = x + 10;
            int rightColumnX = x + width - columnWidth - 10;
            int categoryX = (column == 0) ? leftColumnX : rightColumnX;
            int categoryY = startY + (row * rowHeight);

            // Draw category name
            context.drawTextWithShadow(this.textRenderer, categories[i] + ":",
                categoryX, categoryY, 0xAAAAAA);

            // Calculate available width for stars
            int maxStarWidth = columnWidth - categoryWidth - 5;

            // Draw star rating
            int starsX = categoryX + categoryWidth;
            drawStarRating(context, starsX, categoryY, starSize, starSpacing, ratings[i], maxStarWidth);
        }

        // Calculate footer position (after all rows)
        int footerY = startY + (numRows * rowHeight) + 4;

        // Check if we have enough space for the footer text
        if (footerY + 12 <= y + totalHeight - 5) {
            // Draw a note about who rates the towns - centered
            Text footerText = Text.literal("Rated by server operators").formatted(Formatting.ITALIC);
            int footerWidth = this.textRenderer.getWidth(footerText);
            context.drawTextWithShadow(this.textRenderer, footerText,
                x + (width - footerWidth) / 2, footerY, 0x999999);
        }
    }

    /**
     * Draws a star rating (1-5 stars) with the specified parameters.
     *
     * @param context The draw context
     * @param x The x position to start drawing stars
     * @param y The y position to draw stars
     * @param starSize The size of each star
     * @param spacing The spacing between stars
     * @param rating The rating value (0-5, can be fractional)
     */
    private void drawStarRating(DrawContext context, int x, int y, int starSize, int spacing, float rating) {
        drawStarRating(context, x, y, starSize, spacing, rating, -1);
    }

    /**
     * Draws a star rating (1-5 stars) with the specified parameters and maximum width constraint.
     *
     * @param context The draw context
     * @param x The x position to start drawing stars
     * @param y The y position to draw stars
     * @param starSize The size of each star
     * @param spacing The spacing between stars
     * @param rating The rating value (0-5, can be fractional)
     * @param maxWidth The maximum width available for the rating display, or -1 for no constraint
     */
    private void drawStarRating(DrawContext context, int x, int y, int starSize, int spacing, float rating, int maxWidth) {
        // Colors for stars - using exact colors as requested
        int filledStarColor = 0xFFFFAA00; // Bright gold color for filled stars
        int emptyStarColor = 0xAABBBBBB; // Light gray for empty stars

        // Ensure pixel-perfect positioning
        x = Math.round(x);
        y = Math.round(y);

        // Calculate vertical offset to perfectly center stars with category text
        int verticalOffset = (this.textRenderer.fontHeight - starSize) / 2;
        int adjustedY = y + verticalOffset;

        // Calculate space needed for stars and rating text
        String ratingText = String.format("%.1f", rating);
        int ratingTextWidth = this.textRenderer.getWidth(ratingText);

        // Ensure consistent spacing between stars (3-4 pixels as requested)
        spacing = 4; // Fixed spacing of 4 pixels for consistency

        int totalStarsWidth = 5 * (starSize + spacing) - spacing; // Width of 5 stars
        int totalWidth = totalStarsWidth + 6 + ratingTextWidth; // Stars + spacing + text

        // Adjust star size if needed to fit within maxWidth while maintaining spacing
        if (maxWidth > 0 && totalWidth > maxWidth) {
            // Reduce star size while keeping spacing constant
            int availableWidthForStars = maxWidth - ratingTextWidth - 6;
            starSize = Math.max(10, (availableWidthForStars - (4 * spacing)) / 5); // Minimum size of 10px

            // Recalculate total width
            totalStarsWidth = 5 * (starSize + spacing) - spacing;
        }

        // Draw a subtle background behind the stars for better visibility
        int bgColor = 0x15000000; // Very subtle dark background
        int bgPadding = 3;
        int bgHeight = starSize + 4;
        context.fill(x - bgPadding, adjustedY - bgPadding,
                   x + totalStarsWidth + bgPadding, adjustedY + bgHeight, bgColor);

        // Save the current render state
        context.getMatrices().push();

        // Enable anti-aliasing for smoother rendering
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();

        // Draw 5 stars with precise spacing and alignment
        for (int i = 0; i < 5; i++) {
            // Calculate exact position with pixel-perfect alignment
            int starX = x + i * (starSize + spacing);

            if (i < Math.floor(rating)) {
                // Full star
                drawStar(context, starX, adjustedY, starSize, filledStarColor, true);
            } else if (i == Math.floor(rating) && rating % 1 > 0) {
                // Partial star (half star)
                drawStar(context, starX, adjustedY, starSize, filledStarColor, true, rating % 1);
            } else {
                // Empty star
                drawStar(context, starX, adjustedY, starSize, emptyStarColor, false);
            }
        }

        // Draw numeric rating after the stars with improved styling
        int ratingBgColor = 0x20000000; // Subtle background for the rating text
        int ratingPadding = 3;

        // Draw background for rating text
        context.fill(x + totalStarsWidth + 4 - ratingPadding,
                   y - ratingPadding,
                   x + totalStarsWidth + 4 + ratingTextWidth + ratingPadding,
                   y + this.textRenderer.fontHeight + ratingPadding,
                   ratingBgColor);

        // Draw rating text with shadow for better visibility
        context.drawText(this.textRenderer, ratingText,
            x + totalStarsWidth + 4, y, filledStarColor, true);

        // Restore the previous render state
        RenderSystem.disableBlend();
        context.getMatrices().pop();
    }

    // Star rendering constants
    private static final int STAR_POINTS = 5; // Number of points in the star
    private static final float INNER_RADIUS_RATIO = 0.38f; // Ratio of inner to outer radius for better star shape
    private static final float STAR_SCALE = 0.85f; // Scale factor to ensure star fits within bounds

    /**
     * Draws a star shape.
     *
     * @param context The draw context
     * @param x The x position of the star
     * @param y The y position of the star
     * @param size The size of the star
     * @param color The color of the star
     * @param filled Whether the star should be filled
     */
    private void drawStar(DrawContext context, int x, int y, int size, int color, boolean filled) {
        drawStar(context, x, y, size, color, filled, 1.0f);
    }

    /**
     * Draws a star shape with partial filling.
     *
     * @param context The draw context
     * @param x The x position of the star
     * @param y The y position of the star
     * @param size The size of the star
     * @param color The color of the star
     * @param filled Whether the star should be filled
     * @param fillRatio The ratio of the star to fill (0.0-1.0)
     */
    private void drawStar(DrawContext context, int x, int y, int size, int color, boolean filled, float fillRatio) {
        // Ensure pixel-perfect positioning
        x = Math.round(x);
        y = Math.round(y);

        // Calculate shadow color (30% opacity)
        int shadowColor = (color & 0x00FFFFFF) | 0x4D000000;

        // Define empty star color - this is the standard color for empty stars
        int emptyStarColor = 0xAABBBBBB; // Light gray for empty stars

        // Save the current render state
        context.getMatrices().push();

        // Enable blending for anti-aliasing
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();

        // Calculate star dimensions
        float centerX = x + size / 2.0f;
        float centerY = y + size / 2.0f;
        float outerRadius = size / 2.0f * STAR_SCALE; // Scale to ensure it fits within bounds
        float innerRadius = outerRadius * INNER_RADIUS_RATIO; // Inner radius for the star points

        // Calculate points for the star
        float[][] points = new float[STAR_POINTS * 2][2]; // Outer and inner points

        // Create a proper 5-pointed star
        for (int i = 0; i < STAR_POINTS * 2; i++) {
            // Alternate between outer and inner points
            float radius = (i % 2 == 0) ? outerRadius : innerRadius;

            // Calculate angle - rotate by -90 degrees to start at the top
            // For a 5-pointed star, we need to distribute points evenly
            float angle = (float) Math.toRadians(i * 360.0f / (STAR_POINTS * 2) - 90);

            // Calculate point coordinates with pixel-perfect positioning
            points[i][0] = centerX + radius * (float) Math.cos(angle);
            points[i][1] = centerY + radius * (float) Math.sin(angle);
        }

        // For empty stars, just draw the outline
        if (!filled) {
            // Draw shadow (1px offset with 30% opacity)
            drawStarOutline(context, points, shadowColor, 1, 1);
            // Draw main outline
            drawStarOutline(context, points, color, 0, 0);
            return;
        }

        // For fully filled stars
        if (fillRatio >= 1.0f) {
            // Draw shadow first (1px offset with 30% opacity)
            drawStarOutline(context, points, shadowColor, 1, 1);

            // Use our helper method to draw the filled star
            drawFilledStar(context, points, centerX, centerY, color, shadowColor);
            return;
        }

        // For partially filled stars
        // Special handling for half-stars
        boolean isHalfStar = Math.abs(fillRatio - 0.5f) < 0.05f;

        if (isHalfStar) {
            // IMPROVED APPROACH FOR HALF-STARS:
            // 1. Draw the empty star with uncolored outline and shadow
            // 2. Draw a colored star on top, but only the left half
            // 3. Draw the colored outline only for the left half

            // Calculate shadow color for empty star (based on emptyStarColor)
            int emptyShadowColor = (emptyStarColor & 0x00FFFFFF) | 0x4D000000;

            // Step 1: Draw the complete empty star with uncolored outline and shadow
            // This will be visible for the right half of the star
            drawStarOutline(context, points, emptyShadowColor, 1, 1);
            drawStarOutline(context, points, emptyStarColor, 0, 0);

            // Step 2: Draw the colored version on top, but only the left half
            int centerXInt = Math.round(centerX);
            context.enableScissor(x, y, centerXInt, y + size);

            // Draw the filled colored star (only left half will be visible)
            for (int i = 0; i < STAR_POINTS; i++) {
                // Get the indices of the points that form this triangle
                int p1 = i * 2;
                int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
                int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

                // Draw the triangle
                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p1][0]), Math.round(points[p1][1]),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    color);

                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    Math.round(points[p3][0]), Math.round(points[p3][1]),
                    color);
            }

            // Draw the colored shadow and outline only for the left half
            drawStarOutline(context, points, shadowColor, 1, 1);
            drawStarOutline(context, points, color, 0, 0);

            context.disableScissor();
        } else {
            // For other partial fills
            // Calculate fill width proportionally
            int fillWidth = Math.round(size * fillRatio);

            // First, draw the empty star with outline
            drawStarOutline(context, points, shadowColor, 1, 1);
            drawStarOutline(context, points, color, 0, 0);

            // Now draw the filled portion by clipping
            context.enableScissor(x, y, x + fillWidth, y + size);

            // Draw just the filled triangles (no outline)
            for (int i = 0; i < STAR_POINTS; i++) {
                // Get the indices of the points that form this triangle
                int p1 = i * 2;
                int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
                int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

                // Draw the triangle
                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p1][0]), Math.round(points[p1][1]),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    color);

                fillTriangle(context,
                    Math.round(centerX), Math.round(centerY),
                    Math.round(points[p2][0]), Math.round(points[p2][1]),
                    Math.round(points[p3][0]), Math.round(points[p3][1]),
                    color);
            }

            context.disableScissor();
        }

        // Restore the previous render state
        RenderSystem.disableBlend();
        context.getMatrices().pop();
    }



    /**
     * Fills a triangle with the specified color.
     *
     * @param context The draw context
     * @param x1 The x coordinate of the first point
     * @param y1 The y coordinate of the first point
     * @param x2 The x coordinate of the second point
     * @param y2 The y coordinate of the second point
     * @param x3 The x coordinate of the third point
     * @param y3 The y coordinate of the third point
     * @param color The color to fill with
     */
    private void fillTriangle(DrawContext context, int x1, int y1, int x2, int y2, int x3, int y3, int color) {
        // Simple implementation for small triangles - just fill the bounding box
        // This works well for our star rendering and avoids edge artifacts

        // Find the bounding box of the triangle
        int minX = Math.min(Math.min(x1, x2), x3);
        int minY = Math.min(Math.min(y1, y2), y3);
        int maxX = Math.max(Math.max(x1, x2), x3);
        int maxY = Math.max(Math.max(y1, y2), y3);

        // For very small triangles (which is common in our star rendering),
        // just fill the bounding box for better performance and fewer artifacts
        if (maxX - minX < 3 && maxY - minY < 3) {
            context.fill(minX, minY, maxX + 1, maxY + 1, color);
            return;
        }

        // For larger triangles, use a proper triangle filling algorithm
        // Sort the points by y-coordinate
        if (y1 > y2) {
            int temp = y1; y1 = y2; y2 = temp;
            temp = x1; x1 = x2; x2 = temp;
        }
        if (y1 > y3) {
            int temp = y1; y1 = y3; y3 = temp;
            temp = x1; x1 = x3; x3 = temp;
        }
        if (y2 > y3) {
            int temp = y2; y2 = y3; y3 = temp;
            temp = x2; x2 = x3; x3 = temp;
        }

        // Calculate slopes
        float dx1 = 0, dx2 = 0, dx3 = 0;
        if (y2 - y1 > 0) dx1 = (float)(x2 - x1) / (y2 - y1);
        if (y3 - y1 > 0) dx2 = (float)(x3 - x1) / (y3 - y1);
        if (y3 - y2 > 0) dx3 = (float)(x3 - x2) / (y3 - y2);

        // Start and end x-coordinates
        float startX, endX;

        // First half of the triangle (between y1 and y2)
        startX = endX = x1;
        if (dx1 > dx2) {
            for (int y = y1; y <= y2; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx2;
                endX += dx1;
            }
        } else {
            for (int y = y1; y <= y2; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx1;
                endX += dx2;
            }
        }

        // Second half of the triangle (between y2 and y3)
        startX = x2;
        endX = x1 + (y2 - y1) * dx2;
        if (dx3 > dx2) {
            for (int y = y2; y <= y3; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx3;
                endX += dx2;
            }
        } else {
            for (int y = y2; y <= y3; y++) {
                context.fill(Math.round(startX), y, Math.round(endX) + 1, y + 1, color);
                startX += dx2;
                endX += dx3;
            }
        }
    }

    /**
     * Helper method to draw a filled star.
     *
     * @param context The draw context
     * @param points The array of points defining the star shape
     * @param centerX The x coordinate of the center of the star
     * @param centerY The y coordinate of the center of the star
     * @param color The color to fill with
     * @param shadowColor The color for the shadow
     */
    private void drawFilledStar(DrawContext context, float[][] points, float centerX, float centerY, int color, int shadowColor) {
        // Draw filled shadow first
        for (int i = 0; i < STAR_POINTS; i++) {
            // Get the indices of the points that form this triangle
            int p1 = i * 2;
            int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
            int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

            // Draw the triangle with shadow offset
            fillTriangle(context,
                Math.round(centerX + 1), Math.round(centerY + 1),
                Math.round(points[p1][0] + 1), Math.round(points[p1][1] + 1),
                Math.round(points[p2][0] + 1), Math.round(points[p2][1] + 1),
                shadowColor);

            fillTriangle(context,
                Math.round(centerX + 1), Math.round(centerY + 1),
                Math.round(points[p2][0] + 1), Math.round(points[p2][1] + 1),
                Math.round(points[p3][0] + 1), Math.round(points[p3][1] + 1),
                shadowColor);
        }

        // Draw main filled star
        for (int i = 0; i < STAR_POINTS; i++) {
            // Get the indices of the points that form this triangle
            int p1 = i * 2;
            int p2 = (i * 2 + 1) % (STAR_POINTS * 2);
            int p3 = (i * 2 + 2) % (STAR_POINTS * 2);

            // Draw the triangle
            fillTriangle(context,
                Math.round(centerX), Math.round(centerY),
                Math.round(points[p1][0]), Math.round(points[p1][1]),
                Math.round(points[p2][0]), Math.round(points[p2][1]),
                color);

            fillTriangle(context,
                Math.round(centerX), Math.round(centerY),
                Math.round(points[p2][0]), Math.round(points[p2][1]),
                Math.round(points[p3][0]), Math.round(points[p3][1]),
                color);
        }

        // Draw outline in the same color as the fill for a cleaner look
        drawStarOutline(context, points, color, 0, 0);
    }

    /**
     * Draws the outline of a star shape using the provided points.
     *
     * @param context The draw context
     * @param points The array of points defining the star shape
     * @param color The color to draw with
     * @param offsetX The x offset for shadow effect
     * @param offsetY The y offset for shadow effect
     */
    private void drawStarOutline(DrawContext context, float[][] points, int color, int offsetX, int offsetY) {
        // Draw lines connecting all the points to form the star outline
        for (int i = 0; i < points.length; i++) {
            int nextIndex = (i + 1) % points.length;

            // Get the coordinates of the current and next points
            int x1 = (int)(points[i][0] + offsetX);
            int y1 = (int)(points[i][1] + offsetY);
            int x2 = (int)(points[nextIndex][0] + offsetX);
            int y2 = (int)(points[nextIndex][1] + offsetY);

            // Draw a 1-pixel wide line between the points
            drawLine(context, x1, y1, x2, y2, color);
        }
    }

    /**
     * Draws a 1-pixel wide line between two points.
     *
     * @param context The draw context
     * @param x1 The x coordinate of the first point
     * @param y1 The y coordinate of the first point
     * @param x2 The x coordinate of the second point
     * @param y2 The y coordinate of the second point
     * @param color The color of the line
     */
    private void drawLine(DrawContext context, int x1, int y1, int x2, int y2, int color) {
        // Calculate the direction and length of the line
        int dx = Math.abs(x2 - x1);
        int dy = Math.abs(y2 - y1);

        // Determine which direction to step in
        int sx = x1 < x2 ? 1 : -1;
        int sy = y1 < y2 ? 1 : -1;

        int err = dx - dy;
        int e2;

        // Bresenham's line algorithm
        while (true) {
            // Draw a pixel at the current position
            context.fill(x1, y1, x1 + 1, y1 + 1, color);

            // Check if we've reached the end point
            if (x1 == x2 && y1 == y2) break;

            e2 = 2 * err;
            if (e2 > -dy) {
                err -= dy;
                x1 += sx;
            }
            if (e2 < dx) {
                err += dx;
                y1 += sy;
            }
        }
    }

    /**
     * Draws a modern button background with gradient and subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Create colors for gradient
        int topColor = ((r) << 16) | ((g) << 8) | (b) | 0xFF000000;
        int bottomColor = ((r * 3/4) << 16) | ((g * 3/4) << 8) | (b * 3/4) | 0xFF000000;

        // Draw gradient background
        context.fillGradient(x, y, x + width, y + height, topColor, bottomColor);

        // Draw subtle 3D effect
        int highlightColor = 0x30FFFFFF; // Subtle white highlight
        int shadowColor = 0x30000000; // Subtle shadow

        // Top highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, highlightColor);
        // Left highlight
        context.fill(x + 1, y + 1, x + 2, y + height - 1, highlightColor);

        // Bottom shadow
        context.fill(x + 2, y + height - 2, x + width - 1, y + height - 1, shadowColor);
        // Right shadow
        context.fill(x + width - 2, y + 2, x + width - 1, y + height - 2, shadowColor);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Get player town from client-side manager
            Town playerTown = null;
            if (client.player != null) {
                playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            }

            // Check if we're in the Main subcategory and clicked on the circle
            if (playerTown != null && !subcategories.isEmpty() && selectedSubcategory == subcategories.get(0)) {
                // Debug log the click coordinates and circle position
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Click at (" + (int)mouseX + "," + (int)mouseY + ") - Circle at (" +
                    circleCenterX + "," + circleCenterY + ") with radius " + circleRadius);

                // Check if click is inside the circle
                boolean isInCircle = isPointInCircle((int)mouseX, (int)mouseY, circleCenterX, circleCenterY, circleRadius);
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Click is " + (isInCircle ? "inside" : "outside") + " the circle");

                if (isInCircle) {
                    // Play click sound
                    SoundUtil.playButtonClickSound();

                    // Open the TownImageScreen to select a town image
                    com.pokecobble.Pokecobbleclaim.LOGGER.info("Opening TownImageScreen");
                    this.client.setScreen(new TownImageScreen(this));
                    return true;
                }
            }

            if (playerTown != null) {
                // Calculate content area dimensions
                int leftX = (width - panelWidth) / 2;
                int topY = 10;
                int contentX = leftX + 10;
                int contentY = topY + 25; // Start even closer to header (reduced from 35 to 25)
                int contentWidth = panelWidth - 20;
                int contentHeight = panelHeight - 35; // Extend even closer to bottom (reduced from 45 to 35)

                // Calculate sidebar dimensions - same as in render method
                int sidebarWidth = 90; // Reduced from 130 to 90
                int sidebarX = leftX + 5; // Moved more to the left (from contentX to leftX + 5)
                int sidebarY = contentY;

                // Check for subcategory button clicks
                for (TownSubcategory subcategory : subcategories) {
                    int subcategoryX = subcategory.getX();
                    int subcategoryY = subcategory.getY();
                    int subcategoryHeight = 26; // Updated to match render method

                    if (mouseX >= subcategoryX && mouseX <= subcategoryX + sidebarWidth - 10 &&
                        mouseY >= subcategoryY && mouseY <= subcategoryY + subcategoryHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();
                        // Select this subcategory
                        selectedSubcategory = subcategory;
                        return true;
                    }
                }

                // Check for button clicks in the Players subcategory
                if (selectedSubcategory.getName().equals("Players")) {
                    int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
                    int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width
                    int leftMargin = contentAreaX + 15;
                    int rowHeight = 18; // More compact row height
                    // Define column dimensions (same as in render method)
                    int rankWidth = 50; // Smaller width for rank
                    int nameWidth = 130; // Slightly wider for names
                    int statusWidth = 45; // Narrower
                    int actionWidth = 45; // Narrower

                    // Check for clicks on sorting and filtering buttons
                    int topMargin = 30; // Increased to make room for controls
                    int currentY = contentY + topMargin; // Same as in render method
                    int controlsY = sidebarY + 8; // Position controls inside the content area

                    // Check sort button (cycles through options)
                    int sortButtonWidth = 40;
                    int sortButtonHeight = 14;
                    int sortButtonY = controlsY;
                    int sortButtonX = leftMargin + 5; // Above Rank column

                    if (mouseX >= sortButtonX && mouseX <= sortButtonX + sortButtonWidth &&
                        mouseY >= sortButtonY && mouseY <= sortButtonY + sortButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Cycle through sort types
                        switch (currentSortType) {
                            case RANK:
                                currentSortType = SortType.NAME;
                                break;
                            case NAME:
                                currentSortType = SortType.NEWEST;
                                break;
                            case NEWEST:
                                currentSortType = SortType.OLDEST;
                                break;
                            case OLDEST:
                                currentSortType = SortType.RANK;
                                break;
                        }

                        // Invalidate cache to force regeneration with new sort
                        cachedPlayerList = null;
                        playersScrollOffset = 0; // Reset scroll position
                        return true;
                    }

                    // Check filter button (cycles through options)
                    int filterButtonWidth = 40;
                    int filterButtonHeight = 14;
                    int filterButtonY = controlsY;
                    int filterButtonX = leftMargin + rankWidth + nameWidth + 5; // Above Status column

                    if (mouseX >= filterButtonX && mouseX <= filterButtonX + filterButtonWidth &&
                        mouseY >= filterButtonY && mouseY <= filterButtonY + filterButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Cycle through filter types
                        switch (currentFilterType) {
                            case ALL:
                                currentFilterType = FilterType.ONLINE;
                                break;
                            case ONLINE:
                                currentFilterType = FilterType.OFFLINE;
                                break;
                            case OFFLINE:
                                currentFilterType = FilterType.ALL;
                                break;
                        }

                        // Invalidate cache to force regeneration with new filter
                        cachedPlayerList = null;
                        playersScrollOffset = 0; // Reset scroll position
                        return true;
                    }

                    // Check invite button click
                    if (inviteButtonBounds != null &&
                        mouseX >= inviteButtonBounds[0] && mouseX <= inviteButtonBounds[0] + inviteButtonBounds[2] &&
                        mouseY >= inviteButtonBounds[1] && mouseY <= inviteButtonBounds[1] + inviteButtonBounds[3]) {
                        // Play click sound
                        playClickSound();

                        // Open the invite screen
                        if (playerTown != null) {
                            this.client.setScreen(new InviteScreen(this, playerTown));
                        } else {
                            statusText = Text.literal("You are not in a town!").formatted(Formatting.RED);
                            statusColor = 0xFF5555; // Red
                        }

                        return true;
                    }

                    // Move currentY down to account for the header row
                    currentY += rowHeight;

                    // Calculate list area dimensions
                    int listAreaY = currentY;
                    int listAreaHeight = contentHeight - 20 - rowHeight - rowHeight; // Adjust height to account for header and prevent overflow

                    // Check for scrollbar drag
                    if (mouseX >= leftMargin + contentWidth - 25 && mouseX <= leftMargin + contentWidth - 22 &&
                        mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {

                        // Get dummy player list to calculate total height
                        List<TownPlayer> players = createDummyPlayerList(playerTown);
                        int totalHeight = players.size() * rowHeight;

                        // Calculate max scroll
                        int maxScroll = Math.max(0, totalHeight - listAreaHeight);

                        if (maxScroll > 0) {
                            // Calculate new scroll position based on click position
                            float clickPosition = (float)(mouseY - listAreaY) / listAreaHeight;
                            playersScrollOffset = Math.round(clickPosition * maxScroll);
                            playersScrollOffset = Math.max(0, Math.min(playersScrollOffset, maxScroll));
                            return true;
                        }
                    }

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Players", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Check for clicks on player action buttons
                    int playerY = listAreaY - playersScrollOffset;
                    List<TownPlayer> players = createDummyPlayerList(playerTown);

                    for (TownPlayer player : players) {
                        // Skip if player is completely outside visible area
                        if (playerY + rowHeight < listAreaY || playerY > listAreaY + listAreaHeight) {
                            playerY += rowHeight;
                            continue;
                        }

                        // Check if click is on the info or manage buttons
                        TownPlayerRank rank = player.getRank();
                        boolean isCurrentPlayerOwnerOrAdmin = true; // Placeholder

                        // Check info button (available for all players)
                        int infoX = leftMargin + rankWidth + nameWidth + statusWidth + 5; // Closer to left
                        int infoY = playerY + 1; // Moved up slightly
                        int infoButtonWidth = 35; // Narrower for two buttons
                        int infoButtonHeight = 16;

                        if (mouseX >= infoX && mouseX <= infoX + infoButtonWidth &&
                            mouseY >= infoY && mouseY <= infoY + infoButtonHeight) {
                            // Play click sound
                            playClickSound();
                            // Open player info screen
                            this.client.setScreen(new PlayerInfoScreen(this, player, playerTown));
                            return true;
                        }

                        // Check if there's an active election
                        boolean hasElection = false;
                        if (playerTown != null) {
                            Election election = ElectionManager.getInstance().getElection(playerTown);
                            hasElection = (election != null);
                        }

                        // Check manage/vote button (only for non-owner players if current player has permission)
                        boolean canManagePlayers = false;
                        if (client.player != null && playerTown != null) {
                            TownPlayer currentTownPlayer = playerTown.getPlayer(client.player.getUuid());
                            if (currentTownPlayer != null) {
                                // Check if player is owner (always has all permissions) or has the specific permission
                                canManagePlayers = currentTownPlayer.getRank() == TownPlayerRank.OWNER ||
                                                   currentTownPlayer.hasPermission("Player Management", "Can manage player permissions");
                            }
                        }

                        if (canManagePlayers && rank != TownPlayerRank.OWNER) {
                            int manageX = infoX + infoButtonWidth + 5; // Position after info button
                            int manageY = playerY + 1;
                            int manageButtonWidth = hasElection ? 70 : 45; // Wider for "Vote Mayor"
                            int manageButtonHeight = 16;

                            if (mouseX >= manageX && mouseX <= manageX + manageButtonWidth &&
                                mouseY >= manageY && mouseY <= manageY + manageButtonHeight) {
                                // Play click sound
                                SoundUtil.playButtonClickSound();

                                if (hasElection) {
                                    // Check if player has already voted
                                    boolean hasVoted = false;
                                    UUID votedFor = null;

                                    if (client.player != null) {
                                        UUID playerId = client.player.getUuid();
                                        Election election = ElectionManager.getInstance().getElection(playerTown);
                                        if (election != null) {
                                            hasVoted = election.hasVoted(playerId);
                                            votedFor = election.getVotes().get(playerId);
                                        }
                                    }

                                    if (hasVoted) {
                                        // Show message that player has already voted
                                        String votedPlayerName = "Unknown";
                                        for (TownPlayer townPlayer : players) {
                                            if (townPlayer.getUuid().equals(votedFor)) {
                                                votedPlayerName = townPlayer.getName();
                                                break;
                                            }
                                        }
                                        setStatus("You already voted for " + votedPlayerName, Formatting.RED);
                                    } else {
                                        // Select the Election subcategory
                                        for (TownSubcategory subcategory : subcategories) {
                                            if (subcategory.getName().equals("Election")) {
                                                selectedSubcategory = subcategory;
                                                break;
                                            }
                                        }
                                    }
                                } else {
                                    // Open the player manage screen
                                    this.client.setScreen(new PlayerManageScreen(this, playerTown, player));
                                }
                                return true;
                            }
                        }

                        playerY += rowHeight;
                    }
                }

                // Check for button clicks in the Level subcategory
                if (selectedSubcategory.getName().equals("Level")) {
                    // No interaction needed for the placeholder message
                    // The original code is commented out for easy restoration later
                    /*
                    int contentAreaX = sidebarX + sidebarWidth + 5;
                    int leftMargin = contentAreaX + 15;

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Level", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }
                    */
                    /* Original code commented out for easy restoration later
                    // Get town level (for demo purposes)
                    int townLevel = 3; // Example level
                    int nextLevel = townLevel + 1;
                    int rowHeight = 12; // Match the rendering code
                    int sectionSpacing = 6; // Match the rendering code

                    // Calculate the exact position of the "See All Benefits" button
                    // This should match the position in the renderLevelSubcategory method
                    int currentY = sidebarY + 8; // Start position
                    currentY += rowHeight + 2; // Level title and level info
                    currentY += 6 + 6; // Progress bar + spacing

                    // Next level benefits section
                    currentY += rowHeight; // Next level title
                    if (nextLevel <= 5) {
                        currentY += (rowHeight - 1) * 3; // 3 benefits with slight overlap
                    } else {
                        currentY += rowHeight; // Max level message
                    }

                    // "See All Benefits" button position
                    int seeAllButtonWidth = 90;
                    int seeAllButtonHeight = 14;
                    int seeAllButtonX = leftMargin;
                    int seeAllButtonY = currentY + 1;

                    if (mouseX >= seeAllButtonX && mouseX <= seeAllButtonX + seeAllButtonWidth &&
                        mouseY >= seeAllButtonY && mouseY <= seeAllButtonY + seeAllButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Open the benefits screen
                        this.client.setScreen(new TownBenefitsScreen(this, playerTown));
                        return true;
                    }

                    // Check Contribute button
                    // Calculate the exact position of the Contribute button
                    currentY += seeAllButtonHeight + sectionSpacing - 2; // Match the spacing in the render method

                    int contributeButtonWidth = 100;
                    int contributeButtonHeight = 16;
                    int contributeButtonX = leftMargin;
                    int contributeButtonY = currentY;

                    if (mouseX >= contributeButtonX && mouseX <= contributeButtonX + contributeButtonWidth &&
                        mouseY >= contributeButtonY && mouseY <= contributeButtonY + contributeButtonHeight) {
                        // Play click sound
                        playClickSound();

                        // Open the contribution screen
                        this.client.setScreen(new TownContributeScreen(this, playerTown));
                        return true;
                    }
                    */

                    return false; // No interaction for the placeholder
                }

                // Check for button clicks in the Bank subcategory
                if (selectedSubcategory.getName().equals("Bank")) {
                    int contentAreaX = sidebarX + sidebarWidth + 5;
                    int leftMargin = contentAreaX + 15;
                    int buttonY = sidebarY + 170; // Fixed position for buttons
                    int buttonWidth = 80; // Smaller buttons

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Bank", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }
                    int buttonHeight = 20;
                    int buttonSpacing = 10;

                    // Deposit button
                    int depositX = leftMargin;
                    if (mouseX >= depositX && mouseX <= depositX + buttonWidth &&
                        mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle deposit action (placeholder)
                        return true;
                    }

                    // Withdraw button
                    int withdrawX = depositX + buttonWidth + buttonSpacing;
                    if (mouseX >= withdrawX && mouseX <= withdrawX + buttonWidth &&
                        mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle withdraw action (placeholder)
                        return true;
                    }
                }

                // Check for button clicks in the Main subcategory
                if (!subcategories.isEmpty() && selectedSubcategory == subcategories.get(0)) { // Main subcategory
                    // Leave Town button has been moved to the Settings subcategory
                }

                // Check for button clicks in the Election subcategory
                if (selectedSubcategory.getName().equals("Election")) {
                    // Define content area dimensions
                    int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
                    int contentAreaY = sidebarY;
                    int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width
                    int contentAreaHeight = panelHeight - 35; // Same as in render method

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Election", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Get the election
                    Election election = ElectionManager.getInstance().getElection(playerTown);
                    if (election != null) {
                        // Layout variables
                        int leftMargin = contentAreaX + 15;
                        int rowHeight = 18; // Same as in render method
                        int currentY = contentAreaY + 10; // Same as in render method

                        // Skip header rows
                        currentY += rowHeight * 2; // Title/timer row and column headers

                        // Calculate list area dimensions
                        int listAreaY = currentY;
                        int listAreaHeight = contentAreaHeight - 20 - rowHeight - rowHeight; // Adjust height to account for title/timer and column headers

                        // Check if player has already voted
                        boolean hasVoted = false;
                        UUID votedFor = null;
                        if (client.player != null) {
                            UUID playerId = client.player.getUuid();
                            hasVoted = election.hasVoted(playerId);
                            votedFor = election.getVotes().get(playerId);
                        }

                        // Check for scrollbar drag
                        if (mouseX >= leftMargin + contentAreaWidth - 25 && mouseX <= leftMargin + contentAreaWidth - 22 &&
                            mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {

                            // Get candidates to calculate total height
                            List<UUID> sortedCandidates = election.getSortedCandidates();
                            int totalHeight = sortedCandidates.size() * rowHeight;

                            // Calculate max scroll
                            int maxScroll = Math.max(0, totalHeight - listAreaHeight);

                            if (maxScroll > 0) {
                                // Calculate new scroll position based on click position
                                float clickPosition = (float)(mouseY - listAreaY) / listAreaHeight;
                                electionScrollOffset = Math.round(clickPosition * maxScroll);
                                electionScrollOffset = Math.max(0, Math.min(electionScrollOffset, maxScroll));
                                return true;
                            }
                        }

                        // Check for clicks on candidate vote buttons
                        if (!hasVoted) {
                            // Define column dimensions
                            int rankWidth = 50; // Same as in render method
                            int nameWidth = 130; // Same as in render method
                            int votesWidth = 45; // Same as in render method

                            // Get candidates
                            List<UUID> sortedCandidates = election.getSortedCandidates();

                            // Create a list of TownPlayer objects for the candidates
                            List<TownPlayer> candidates = new ArrayList<>();
                            for (UUID candidateId : sortedCandidates) {
                                // Get player name
                                String playerName = getPlayerName(candidateId);

                                // Get player rank
                                TownPlayerRank rank = playerTown.getPlayerRank(candidateId);
                                if (rank == null) {
                                    rank = TownPlayerRank.MEMBER; // Default to member if rank not found
                                }

                                // Create TownPlayer object
                                TownPlayer candidate = new TownPlayer(candidateId, playerName, rank, true);
                                candidates.add(candidate);
                            }

                            // Check for clicks on vote buttons
                            int candidateY = listAreaY - electionScrollOffset;

                            for (TownPlayer candidate : candidates) {
                                // Skip if candidate is completely outside visible area
                                if (candidateY + rowHeight < listAreaY || candidateY > listAreaY + listAreaHeight) {
                                    candidateY += rowHeight;
                                    continue;
                                }

                                // Check info button
                                int infoX = leftMargin + rankWidth + nameWidth + votesWidth + 5;
                                int infoY = candidateY + 1;
                                int infoButtonWidth = 30;
                                int infoButtonHeight = 16;

                                if (mouseX >= infoX && mouseX <= infoX + infoButtonWidth &&
                                    mouseY >= infoY && mouseY <= infoY + infoButtonHeight) {
                                    // Play click sound
                                    playClickSound();

                                    // Log the click for debugging
                                    System.out.println("Info button clicked for: " + candidate.getName());

                                    try {
                                        // Directly set the screen
                                        PlayerInfoScreen infoScreen = new PlayerInfoScreen(this, candidate, playerTown);
                                        this.client.setScreen(infoScreen);

                                        // Set a status message as a fallback
                                        setStatus("Click again if the player info doesn't appear", Formatting.YELLOW);
                                    } catch (Exception e) {
                                        // Log any errors
                                        System.err.println("Error opening player info screen: " + e.getMessage());
                                        e.printStackTrace();

                                        // Show an error message to the user
                                        setStatus("Error opening player info. Try again.", Formatting.RED);
                                    }

                                    return true;
                                }

                                // Check vote button
                                int voteX = infoX + infoButtonWidth + 5;
                                int voteY = candidateY + 1;
                                int voteButtonWidth = 30;
                                int voteButtonHeight = 16;

                                // Check if player has permission to vote
                                boolean canVote = false;
                                if (client.player != null && playerTown != null) {
                                    TownPlayer currentTownPlayer = playerTown.getPlayer(client.player.getUuid());
                                    if (currentTownPlayer != null) {
                                        // Everyone in the town can vote
                                        canVote = true;
                                    }
                                }

                                if (canVote && mouseX >= voteX && mouseX <= voteX + voteButtonWidth &&
                                    mouseY >= voteY && mouseY <= voteY + voteButtonHeight) {
                                    // Play click sound
                                    playClickSound();

                                    // Log the click for debugging
                                    System.out.println("Vote button clicked for: " + candidate.getName());

                                    try {
                                        // Directly set the screen without using a lambda
                                        VoteConfirmationScreen confirmScreen = new VoteConfirmationScreen(this, playerTown, candidate);
                                        this.client.setScreen(confirmScreen);

                                        // Set a status message as a fallback in case the screen doesn't open
                                        setStatus("Click again if the vote confirmation doesn't appear", Formatting.YELLOW);
                                    } catch (Exception e) {
                                        // Log any errors
                                        System.err.println("Error opening vote confirmation screen: " + e.getMessage());
                                        e.printStackTrace();

                                        // Show an error message to the user
                                        setStatus("Error opening vote confirmation. Try again.", Formatting.RED);
                                    }

                                    return true;
                                }

                                candidateY += rowHeight;
                            }
                        }
                    }
                }

                // Check for button clicks in the Claims subcategory
                if (selectedSubcategory.getName().equals("Claims")) {
                    int contentAreaX = sidebarX + sidebarWidth + 5; // Position after sidebar
                    int contentAreaWidth = leftX + panelWidth - contentAreaX - 10; // Fill remaining width
                    int leftMargin = contentAreaX + 15;
                    int rowHeight = 14; // Reduced from 16 to 14

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Claims", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }
                    int rowSpacing = 5; // Added to match renderClaimsSubcategory method
                    int buttonHeight = 18; // Reduced from 20 to 18
                    int currentY = contentY + 10; // Reduced from 15 to 10

                    // Calculate exact positions to match the render method
                    // Title
                    currentY += rowHeight + 3; // Title (reduced spacing)

                    // Claim Usage section
                    currentY += rowHeight; // Claim Usage header
                    currentY += rowHeight; // Used and Available claims (now in one row)
                    currentY += rowSpacing; // Section spacing

                    // Claim Tools section
                    currentY += rowHeight + 3; // Claim Tools header (reduced spacing)

                    // Two-column layout for buttons
                    int buttonSpacing = 5;
                    int smallerButtonWidth = (contentWidth - 60) / 2;

                    // Check claim tool button (left column)
                    int claimToolX = leftMargin + 5;
                    int claimToolY = currentY;
                    if (mouseX >= claimToolX && mouseX <= claimToolX + smallerButtonWidth &&
                        mouseY >= claimToolY && mouseY <= claimToolY + buttonHeight) {
                        // Play click sound
                        playClickSound();
                        // Activate the claim tool and close all screens
                        ClaimTool.getInstance().activate(playerTown);
                        // No need to call this.close() as ClaimTool.activate() already closes all screens
                        return true;
                    }

                    // Check view boundaries button (right column)
                    int viewBoundariesX = leftMargin + smallerButtonWidth + buttonSpacing + 5;
                    int viewBoundariesY = currentY;
                    if (mouseX >= viewBoundariesX && mouseX <= viewBoundariesX + smallerButtonWidth &&
                        mouseY >= viewBoundariesY && mouseY <= viewBoundariesY + buttonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle view boundaries action (placeholder)
                        System.out.println("View Boundaries clicked");
                        return true;
                    }
                    currentY += buttonHeight + 4; // Reduced spacing

                    // Check claim settings button (left column)
                    int claimSettingsX = leftMargin + 5;
                    int claimSettingsY = currentY;
                    if (mouseX >= claimSettingsX && mouseX <= claimSettingsX + smallerButtonWidth &&
                        mouseY >= claimSettingsY && mouseY <= claimSettingsY + buttonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();
                        // Open the claim tag screen with this screen as parent
                        this.client.setScreen(new com.pokecobble.town.gui.ClaimTagScreen(this, playerTown));
                        return true;
                    }

                    // Check remove all claims button (right column)
                    int removeClaimsX = leftMargin + smallerButtonWidth + buttonSpacing + 5;
                    int removeClaimsY = currentY;
                    if (mouseX >= removeClaimsX && mouseX <= removeClaimsX + smallerButtonWidth &&
                        mouseY >= removeClaimsY && mouseY <= removeClaimsY + buttonHeight) {
                        // Play click sound
                        playClickSound();
                        // Handle remove all claims action (placeholder)
                        System.out.println("Remove All Claims clicked");
                        return true;
                    }

                    // Skip to claim history section - calculate exact position
                    currentY += buttonHeight + rowSpacing;

                    // Check more info button beside claim history
                    int moreInfoWidth = 60;
                    int moreInfoHeight = 14;
                    int moreInfoX = leftMargin + 120; // Position near the title
                    int moreInfoY = currentY - 2; // Align with the Claim History title
                    if (mouseX >= moreInfoX && mouseX <= moreInfoX + moreInfoWidth &&
                        mouseY >= moreInfoY && mouseY <= moreInfoY + moreInfoHeight) {
                        // Play click sound
                        playClickSound();

                        // Log the click for debugging
                        System.out.println("Claim History More Info button clicked");

                        // Open the claim history screen using a direct method
                        openClaimHistoryScreen(playerTown);

                        return true;
                    }
                }

                // Check for button clicks in the Settings subcategory
                if (selectedSubcategory.getName().equals("Settings")) {
                    // Calculate content area dimensions to match rendering exactly
                    int settingsContentX = sidebarX + sidebarWidth + 5;
                    int settingsContentY = sidebarY;
                    int settingsContentWidth = leftX + panelWidth - settingsContentX - 10;
                    int settingsContentHeight = panelHeight - 35;

                    // Match the exact rendering calculations from renderSettingsSubcategory
                    int leftMargin = settingsContentX + 10; // Match rendering: contentX + 10
                    int rowHeight = 14; // More compact - match rendering
                    int currentY = settingsContentY + 10; // Match rendering: contentY + 10

                    // Check if player has permission to interact with this subcategory
                    if (!hasPermissionForSubcategory("Settings", playerTown)) {
                        return false; // Don't handle clicks if no permission
                    }

                    // Skip title and status section to get to buttons (match rendering exactly)
                    currentY += rowHeight + 8; // After title
                    currentY += rowHeight; // Access status
                    currentY += rowHeight + 15; // Player count + spacing

                    // Compact button layout - calculate responsive sizes (match rendering exactly)
                    int availableWidth = settingsContentWidth - 20;
                    int buttonSpacing = 8;
                    int numButtons = 2; // Main action buttons
                    int buttonWidth = Math.min(100, (availableWidth - buttonSpacing) / numButtons);
                    int buttonHeight = 18;

                    // Main action buttons row
                    int button1X = leftMargin;
                    int button2X = leftMargin + buttonWidth + buttonSpacing;

                    // Advanced Settings button
                    if (mouseX >= button1X && mouseX <= button1X + buttonWidth &&
                        mouseY >= currentY && mouseY <= currentY + buttonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Open the advanced settings screen
                        this.client.setScreen(new TownSettingsScreen(this, playerTown));
                        return true;
                    }

                    // Quick Toggle Access button
                    if (mouseX >= button2X && mouseX <= button2X + buttonWidth &&
                        mouseY >= currentY && mouseY <= currentY + buttonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Toggle town access
                        if (playerTown.getJoinType() == Town.JoinType.OPEN) {
                            playerTown.setJoinType(Town.JoinType.CLOSED);
                        } else {
                            playerTown.setJoinType(Town.JoinType.OPEN);
                        }

                        // Update town settings
                        com.pokecobble.town.config.TownSettingsManager.setTownSetting(
                            playerTown.getId(), "isOpen", playerTown.getJoinType() == Town.JoinType.OPEN);

                        // Save the town
                        TownManager.getInstance().saveTown(playerTown);

                        return true;
                    }

                    // Leave Town button (compact layout) - match rendering exactly
                    int leaveButtonWidth = 70;
                    int leaveButtonHeight = 16;
                    int leaveButtonX = settingsContentX + settingsContentWidth - leaveButtonWidth - 10;
                    int leaveButtonY = settingsContentY + settingsContentHeight - leaveButtonHeight - 10;

                    if (mouseX >= leaveButtonX && mouseX <= leaveButtonX + leaveButtonWidth &&
                        mouseY >= leaveButtonY && mouseY <= leaveButtonY + leaveButtonHeight) {
                        // Play click sound
                        SoundUtil.playButtonClickSound();

                        // Check if player is mayor
                        boolean isMayor = false;
                        if (client.player != null) {
                            UUID playerId = client.player.getUuid();
                            isMayor = playerTown.getPlayerRank(playerId) == TownPlayerRank.OWNER;
                        }

                        // Show leave town confirmation screen
                        this.client.setScreen(new LeaveTownConfirmationScreen(this, playerTown, isMayor));
                        return true;
                    }
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Get player town
        Town playerTown = null;
        if (client.player != null) {
            playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
        }

        if (playerTown != null) {
            // Handle scrolling in the Players subcategory
            if (selectedSubcategory.getName().equals("Players")) {
                // Calculate content area dimensions
                int leftX = (width - panelWidth) / 2;
                int topY = 10;
                int contentX = leftX + 10;
                int contentY = topY + 35; // Start closer to header
                int contentWidth = panelWidth - 20;
                int contentHeight = panelHeight - 45; // Extend closer to bottom

                // Calculate sidebar dimensions
                int sidebarWidth = 100; // Narrower sidebar
                int sidebarX = contentX;
                int sidebarY = contentY;

                // Calculate content area
                int contentAreaX = sidebarX + sidebarWidth + 5;
                int leftMargin = contentAreaX + 15;
                int rowHeight = 18; // More compact row height
                int topMargin = 30; // Same as in render method
                int currentY = contentY + topMargin; // Same as in render method

                // Move currentY down to account for the header row
                currentY += rowHeight;

                // Calculate list area dimensions
                int listAreaY = currentY;
                int listAreaHeight = contentHeight - 20 - rowHeight - rowHeight; // Adjust height to account for header and prevent overflow

                // Check if mouse is in the list area
                if (mouseX >= leftMargin && mouseX <= leftMargin + contentWidth - 30 &&
                    mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {

                    // Get dummy player list to calculate total height
                    List<TownPlayer> players = createDummyPlayerList(playerTown);
                    int totalHeight = players.size() * rowHeight;

                    // Calculate max scroll
                    int maxScroll = Math.max(0, totalHeight - listAreaHeight);

                    // Update scroll offset with smoother scrolling
                    playersScrollOffset -= (int) (amount * 10); // Scroll amount

                    // Ensure scroll offset stays within bounds
                    playersScrollOffset = Math.max(0, Math.min(playersScrollOffset, maxScroll));

                    return true;
                }
            }

            // Handle scrolling in the Election subcategory
            if (selectedSubcategory.getName().equals("Election")) {
                // Calculate content area dimensions
                int leftX = (width - panelWidth) / 2;
                int topY = 10;
                int contentX = leftX + 10;
                int contentY = topY + 35; // Start closer to header
                int contentWidth = panelWidth - 20;
                int contentHeight = panelHeight - 45; // Extend closer to bottom

                // Calculate sidebar dimensions
                int sidebarWidth = 100; // Narrower sidebar
                int sidebarX = contentX;
                int sidebarY = contentY;

                // Calculate content area
                int contentAreaX = sidebarX + sidebarWidth + 5;
                int leftMargin = contentAreaX + 15;
                int rowHeight = 18; // Same as in render method
                int currentY = contentY + 10; // Same as in render method

                // Skip header rows
                currentY += rowHeight * 2; // Title/timer row and column headers

                // Calculate list area dimensions
                int listAreaY = currentY;
                int listAreaHeight = contentHeight - 20 - rowHeight - rowHeight; // Adjust height to account for title/timer and column headers

                // Check if mouse is in the list area
                if (mouseX >= leftMargin && mouseX <= leftMargin + contentWidth - 30 &&
                    mouseY >= listAreaY && mouseY <= listAreaY + listAreaHeight) {

                    // Get the election
                    Election election = ElectionManager.getInstance().getElection(playerTown);
                    if (election != null) {
                        // Get candidates to calculate total height
                        List<UUID> sortedCandidates = election.getSortedCandidates();
                        int totalHeight = sortedCandidates.size() * rowHeight;

                        // Calculate max scroll
                        int maxScroll = Math.max(0, totalHeight - listAreaHeight);

                        // Update scroll offset with smoother scrolling
                        electionScrollOffset -= (int) (amount * 10); // Scroll amount

                        // Ensure scroll offset stays within bounds
                        electionScrollOffset = Math.max(0, Math.min(electionScrollOffset, maxScroll));

                        return true;
                    }
                }
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Called when the search field text changes.
     */
    private void onSearchFieldChanged(String newText) {
        searchQuery = newText.trim();
        // Invalidate cache to force regeneration with new search query
        cachedPlayerList = null;
        // Reset scroll position when search changes
        playersScrollOffset = 0;
    }

    /**
     * Sets the status text.
     *
     * @param message The message to display
     * @param formatting The formatting to apply
     */
    public void setStatus(String message, Formatting formatting) {
        statusText = Text.literal(message).formatted(formatting);

        // Set color based on formatting
        if (formatting == Formatting.RED) {
            statusColor = 0xFF5555; // Red
        } else if (formatting == Formatting.GREEN) {
            statusColor = 0x55FF55; // Green
        } else if (formatting == Formatting.YELLOW || formatting == Formatting.GOLD) {
            statusColor = 0xFFFF55; // Yellow
        } else {
            statusColor = 0xFFFFFF; // White
        }
    }

    /**
     * Opens the claim history screen for a town.
     * This is a separate method to avoid issues with direct instantiation.
     *
     * @param town The town to show history for
     */
    private void openClaimHistoryScreen(Town town) {
        try {
            // Create the screen
            Screen historyScreen = new ClaimHistoryScreen(this, town);

            // Schedule the screen change for the next tick to avoid any rendering issues
            MinecraftClient.getInstance().execute(() -> {
                MinecraftClient.getInstance().setScreen(historyScreen);
            });
        } catch (Exception e) {
            // Log any errors
            System.err.println("Error opening claim history screen: " + e.getMessage());
            e.printStackTrace();

            // Show an error message to the user
            setStatus("Error opening history screen. Try again.", Formatting.RED);
        }
    }

    /**
     * Renders a placeholder message for the Level subcategory.
     * This is a temporary implementation that will be replaced with the actual functionality in a future update.
     *
     * @param context The draw context
     * @param contentX The X position of the content area
     * @param contentY The Y position of the content area
     * @param contentWidth The width of the content area
     * @param contentHeight The height of the content area
     */
    private void renderLevelPlaceholderMessage(DrawContext context, int contentX, int contentY, int contentWidth, int contentHeight) {
        // Calculate usable area with margins
        int margin = 20;
        int usableX = contentX + margin;
        int usableY = contentY + margin;
        int usableWidth = contentWidth - (margin * 2);
        int usableHeight = contentHeight - (margin * 2);

        // Draw a nice background
        context.fill(usableX, usableY, usableX + usableWidth, usableY + usableHeight, 0x30000000);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Town Level System").formatted(Formatting.BOLD, Formatting.GOLD),
            usableX + usableWidth / 2, usableY + 20, 0xFFFFAA00);

        // Draw coming soon message
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("This feature will be available in a future update!").formatted(Formatting.ITALIC),
            usableX + usableWidth / 2, usableY + 50, 0xFFFFFF);

        // Draw feature description - shorter lines that fit within the content area
        String[] description = {
            "The Town Level system will allow towns to",
            "gain experience and level up through",
            "various activities and challenges.",
            "",
            "Each level will unlock new benefits such as",
            "increased claim limits and special structures."
        };

        // Calculate vertical spacing to ensure text fits
        int totalTextHeight = description.length * 12; // 12 pixels per line
        int startY = usableY + (usableHeight / 2); // Start from the middle

        // Draw each line of the description
        int descY = startY;
        for (String line : description) {
            context.drawCenteredTextWithShadow(this.textRenderer, line,
                usableX + usableWidth / 2, descY, 0xFFCCCCCC);
            descY += 12;
        }
    }

    /**
     * Refreshes the player list to ensure ranks are up to date.
     * This should be called when the election status changes or when town player data is updated.
     */
    public void refreshPlayerList() {
        // Clear the cached player list to force a refresh
        lastTownId = null;
        cachedPlayerList = null;

        // Reset scroll positions
        playersScrollOffset = 0;
        electionScrollOffset = 0;
    }

    /**
     * Implements RefreshableComponent interface.
     */
    @Override
    public void refresh() {
        refreshPlayerList();

        // Request fresh data from server
        TownNetworkHandler.requestTownData();
    }

    /**
     * Gets the data types this component depends on.
     */
    @Override
    public java.util.Set<String> getDependentDataTypes() {
        return java.util.Set.of("town_data", "player_data", "election_data");
    }

    /**
     * Called when town player data is updated.
     * Implements the TownPlayerUpdateCallback interface.
     *
     * @param townId The ID of the town that was updated
     */
    @Override
    public void onTownPlayerUpdate(UUID townId) {
        // Check if this is the player's town
        if (client.player != null) {
            Town playerTown = com.pokecobble.town.client.ClientTownManager.getInstance().getPlayerTown();
            if (playerTown != null && playerTown.getId().equals(townId)) {
                // Refresh the player list
                refreshPlayerList();
            }
        }
    }

    /**
     * Called when election data is updated.
     * Implements the ElectionUpdateCallback interface.
     *
     * @param townId The ID of the town whose election was updated
     */
    @Override
    public void onElectionUpdate(UUID townId) {
        // Check if this is the player's town
        if (client.player != null) {
            Town playerTown = TownManager.getInstance().getPlayerTown(client.player.getUuid());
            if (playerTown != null && playerTown.getId().equals(townId)) {
                // Refresh the election subcategory by refreshing all subcategories
                setupSubcategories();
                // Reset election scroll offset
                electionScrollOffset = 0;
            }
        }
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Gets the display text for a town player rank.
     *
     * @param rank The rank
     * @return The display text
     */
    private String getRankText(TownPlayerRank rank) {
        switch (rank) {
            case OWNER:
                return "Mayor";
            case ADMIN:
                return "Deputy";
            case MODERATOR:
                return "Council";
            case MEMBER:
                return "Resident";
            case VISITOR:
                return "Citizen";
            default:
                return "Unknown";
        }
    }

    @Override
    public void resize(MinecraftClient client, int width, int height) {
        super.resize(client, width, height);

        // Recalculate panel dimensions
        panelWidth = Math.min(width - 20, 1100);
        panelHeight = height - 20;

        // Reinitialize subcategory positions
        initializeSubcategoryPositions();
    }

    @Override
    public void close() {
        // Clear the player list cache when closing the screen
        lastTownId = null;
        cachedPlayerList = null;
        this.client.setScreen(parent);
    }



    /**
     * Checks if a point is inside a circle.
     *
     * @param pointX The x coordinate of the point
     * @param pointY The y coordinate of the point
     * @param circleX The x coordinate of the circle center
     * @param circleY The y coordinate of the circle center
     * @param radius The radius of the circle
     * @return True if the point is inside the circle, false otherwise
     */
    private boolean isPointInCircle(int pointX, int pointY, int circleX, int circleY, int radius) {
        int dx = pointX - circleX;
        int dy = pointY - circleY;
        return dx * dx + dy * dy <= radius * radius;
    }

    /**
     * Draws the default town icon (house) in the center of the circle.
     *
     * @param context The draw context
     * @param centerX The x coordinate of the center
     * @param centerY The y coordinate of the center
     */
    private void drawDefaultTownIcon(DrawContext context, float centerX, float centerY) {
        String townIcon = "🏠"; // House icon
        int iconWidth = this.textRenderer.getWidth(townIcon);
        context.drawTextWithShadow(this.textRenderer, townIcon,
            (int)(centerX - iconWidth/2), (int)(centerY - 8), 0xFFFFFF);
    }

    /**
     * Draws an image with a circular mask.
     *
     * @param context The draw context
     * @param imageId The image identifier
     * @param x The x position to draw the image
     * @param y The y position to draw the image
     * @param width The width of the image
     * @param height The height of the image
     * @param circleCenterX The x center of the circular mask
     * @param circleCenterY The y center of the circular mask
     * @param circleRadius The radius of the circular mask
     */
    private void drawCircularImage(DrawContext context, Identifier imageId, int x, int y, int width, int height,
                                  int circleCenterX, int circleCenterY, int circleRadius) {
        // First, fill the entire circle area with the circle color
        // This ensures no image parts will be visible outside the circle
        int circleColor = 0xFF2196F3; // Blue color for the circle
        drawCircle(context, circleCenterX, circleCenterY, circleRadius, circleColor, true);

        // Calculate the bounding box of the circle
        int left = circleCenterX - circleRadius;
        int top = circleCenterY - circleRadius;
        int right = circleCenterX + circleRadius;
        int bottom = circleCenterY + circleRadius;

        // Enable scissor to the bounding box of the circle
        context.enableScissor(left, top, right, bottom);

        // For each pixel in the circle, check if it's inside the circle
        // If it is, draw the corresponding pixel from the image
        int radiusSquared = circleRadius * circleRadius;

        // Draw the image only inside the circle
        for (int py = top; py < bottom; py++) {
            for (int px = left; px < right; px++) {
                int dx = px - circleCenterX;
                int dy = py - circleCenterY;
                if (dx * dx + dy * dy <= radiusSquared) {
                    // This point is inside the circle, calculate the corresponding image pixel
                    int imgX = px - x;
                    int imgY = py - top;

                    // Only draw if the pixel is within the image bounds
                    if (imgX >= 0 && imgX < width && imgY >= 0 && imgY < height) {
                        // Draw a single pixel from the image
                        // Since we can't access individual pixels, we'll draw a 1x1 texture
                        context.drawTexture(imageId,
                                          px, py, // destination position
                                          imgX, imgY, // source position in texture
                                          1, 1, // size to draw
                                          width, height); // texture dimensions
                    }
                }
            }
        }

        // Disable scissor
        context.disableScissor();
    }

    /**
     * Draws a circle using a series of small rectangles.
     *
     * @param context The draw context
     * @param centerX The x coordinate of the circle center
     * @param centerY The y coordinate of the circle center
     * @param radius The radius of the circle
     * @param color The color of the circle (ARGB format)
     * @param filled Whether to fill the circle or just draw the outline
     */
    private void drawCircle(DrawContext context, float centerX, float centerY, float radius, int color, boolean filled) {
        int x0 = (int)(centerX - radius);
        int y0 = (int)(centerY - radius);
        int x1 = (int)(centerX + radius);
        int y1 = (int)(centerY + radius);

        if (filled) {
            // Draw a filled circle using small rectangles
            for (int x = x0; x <= x1; x++) {
                for (int y = y0; y <= y1; y++) {
                    int dx = x - (int)centerX;
                    int dy = y - (int)centerY;
                    if (dx * dx + dy * dy <= radius * radius) {
                        context.fill(x, y, x + 1, y + 1, color);
                    }
                }
            }
        } else {
            // Draw just the outline
            for (int x = x0; x <= x1; x++) {
                for (int y = y0; y <= y1; y++) {
                    int dx = x - (int)centerX;
                    int dy = y - (int)centerY;
                    int distSq = dx * dx + dy * dy;
                    if (distSq <= radius * radius && distSq >= (radius - 1) * (radius - 1)) {
                        context.fill(x, y, x + 1, y + 1, color);
                    }
                }
            }
        }
    }

    @Override
    public void removed() {
        super.removed();

        // Unregister from town player updates when the screen is closed
        TownDataSynchronizer.unregisterPlayerUpdateCallback(this);

        // Unregister from election updates
        ElectionNetworkHandler.unregisterElectionUpdateCallback(this);
    }
}
